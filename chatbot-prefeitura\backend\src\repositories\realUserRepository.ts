/**
 * 🗄️ Real User Repository
 * Implementação real conectada ao PostgreSQL - Preparada para integração
 */

import { BaseUserRepository } from './userRepository'
import { User, CreateUserDto, UpdateUserDto, UserSearchParams, UserStats, UserRepositoryUtils } from '../types/user.types'
import { PostgreSQLService } from '../services/database/postgresql'

/**
 * 🗄️ Repositório Real conectado ao PostgreSQL
 */
export class RealUserRepository extends BaseUserRepository {
  private postgresService: PostgreSQLService
  
  constructor() {
    super()
    this.postgresService = new PostgreSQLService()
  }

  /**
   * 🔍 Buscar usuário por ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      const query = `
        SELECT * FROM usuarios 
        WHERE id = $1 AND excluido = false
      `
      const result = await this.postgresService.query(query, [id])
      
      if (result.rows.length === 0) return null
      
      return this.mapDatabaseRowToUser(result.rows[0])
    } catch (error) {
      console.error('Erro ao buscar usuário por ID:', error)
      return null
    }
  }

  /**
   * 📧 Buscar usuário por email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const normalizedEmail = UserRepositoryUtils.normalizeEmail(email)
      const query = `
        SELECT * FROM usuarios 
        WHERE LOWER(email) = $1 AND excluido = false
      `
      const result = await this.postgresService.query(query, [normalizedEmail])
      
      if (result.rows.length === 0) return null
      
      return this.mapDatabaseRowToUser(result.rows[0])
    } catch (error) {
      console.error('Erro ao buscar usuário por email:', error)
      return null
    }
  }

  /**
   * 🆔 Buscar usuário por CPF
   */
  async findByCPF(cpf: string): Promise<User | null> {
    try {
      const sanitizedCPF = UserRepositoryUtils.sanitizeCPF(cpf)
      const query = `
        SELECT * FROM usuarios 
        WHERE cpf = $1 AND excluido = false
      `
      const result = await this.postgresService.query(query, [sanitizedCPF])
      
      if (result.rows.length === 0) return null
      
      return this.mapDatabaseRowToUser(result.rows[0])
    } catch (error) {
      console.error('Erro ao buscar usuário por CPF:', error)
      return null
    }
  }

  /**
   * 📋 Buscar múltiplos usuários com filtros
   */
  async findMany(params: UserSearchParams): Promise<User[]> {
    try {
      const { conditions, values } = UserRepositoryUtils.buildSearchQuery(params)
      
      let query = `SELECT * FROM usuarios`
      
      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`
      }
      
      query += ` ORDER BY created_at DESC`
      
      // Paginação
      const limit = params.limit || 50
      const offset = params.offset || 0
      
      query += ` LIMIT $${values.length + 1} OFFSET $${values.length + 2}`
      values.push(limit, offset)
      
      const result = await this.postgresService.query(query, values)
      
      return result.rows.map(row => this.mapDatabaseRowToUser(row))
    } catch (error) {
      console.error('Erro ao buscar usuários:', error)
      return []
    }
  }

  /**
   * 🔢 Contar usuários com filtros
   */
  async count(params: Omit<UserSearchParams, 'limit' | 'offset'>): Promise<number> {
    try {
      const { conditions, values } = UserRepositoryUtils.buildSearchQuery(params)
      
      let query = `SELECT COUNT(*) as total FROM usuarios`
      
      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`
      }
      
      const result = await this.postgresService.query(query, values)
      return parseInt(result.rows[0].total)
    } catch (error) {
      console.error('Erro ao contar usuários:', error)
      return 0
    }
  }

  /**
   * ➕ Criar novo usuário
   */
  async create(userData: CreateUserDto): Promise<User> {
    try {
      const hashedPassword = await this.hashPassword(userData.senha)
      const sanitizedCPF = UserRepositoryUtils.sanitizeCPF(userData.cpf)
      const now = new Date()
      
      const query = `
        INSERT INTO usuarios (
          nome, email, cpf, senha, conta_ativa, servidor, 
          redefinir_senha, cadastro_manual, excluido,
          visualizar_processo_sigiloso, foto_perfil,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING *
      `
      
      const values = [
        userData.nome,
        userData.email || null,
        sanitizedCPF,
        hashedPassword,
        true, // conta_ativa
        userData.servidor || false,
        false, // redefinir_senha
        true, // cadastro_manual
        false, // excluido
        false, // visualizar_processo_sigiloso
        userData.foto_perfil || null,
        now, // created_at
        now // updated_at
      ]
      
      const result = await this.postgresService.query(query, values)
      return this.mapDatabaseRowToUser(result.rows[0])
    } catch (error) {
      console.error('Erro ao criar usuário:', error)
      throw new Error('Erro ao criar usuário')
    }
  }

  /**
   * ✏️ Atualizar usuário
   */
  async update(id: string, userData: UpdateUserDto): Promise<User | null> {
    try {
      const updates: string[] = []
      const values: any[] = []
      let valueIndex = 1
      
      if (userData.nome) {
        updates.push(`nome = $${valueIndex}`)
        values.push(userData.nome)
        valueIndex++
      }
      
      if (userData.email !== undefined) {
        updates.push(`email = $${valueIndex}`)
        values.push(userData.email)
        valueIndex++
      }
      
      if (userData.foto_perfil !== undefined) {
        updates.push(`foto_perfil = $${valueIndex}`)
        values.push(userData.foto_perfil)
        valueIndex++
      }
      
      if (userData.conta_ativa !== undefined) {
        updates.push(`conta_ativa = $${valueIndex}`)
        values.push(userData.conta_ativa)
        valueIndex++
      }
      
      if (userData.nova_senha) {
        const hashedPassword = await this.hashPassword(userData.nova_senha)
        updates.push(`senha = $${valueIndex}`)
        values.push(hashedPassword)
        valueIndex++
        
        updates.push(`redefinir_senha = false`)
        updates.push(`nova_senha = null`)
      }
      
      if (userData.redefinir_senha !== undefined) {
        updates.push(`redefinir_senha = $${valueIndex}`)
        values.push(userData.redefinir_senha)
        valueIndex++
      }
      
      // Sempre atualizar updated_at
      updates.push(`updated_at = $${valueIndex}`)
      values.push(new Date())
      valueIndex++
      
      if (updates.length === 0) {
        return this.findById(id) // Nenhuma atualização, retorna usuário atual
      }
      
      const query = `
        UPDATE usuarios 
        SET ${updates.join(', ')}
        WHERE id = $${valueIndex} AND excluido = false
        RETURNING *
      `
      values.push(id)
      
      const result = await this.postgresService.query(query, values)
      
      if (result.rows.length === 0) return null
      
      return this.mapDatabaseRowToUser(result.rows[0])
    } catch (error) {
      console.error('Erro ao atualizar usuário:', error)
      return null
    }
  }

  /**
   * 🕒 Atualizar último login
   */
  async updateLastLogin(id: string): Promise<void> {
    try {
      const query = `
        UPDATE usuarios 
        SET updated_at = $1
        WHERE id = $2
      `
      await this.postgresService.query(query, [new Date(), id])
    } catch (error) {
      console.error('Erro ao atualizar último login:', error)
    }
  }

  /**
   * 🗑️ Soft delete
   */
  async softDelete(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE usuarios 
        SET excluido = true, conta_ativa = false, updated_at = $1
        WHERE id = $2 AND excluido = false
      `
      const result = await this.postgresService.query(query, [new Date(), id])
      return result.rowCount > 0
    } catch (error) {
      console.error('Erro ao excluir usuário:', error)
      return false
    }
  }

  /**
   * ♻️ Restaurar usuário
   */
  async restore(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE usuarios 
        SET excluido = false, conta_ativa = true, updated_at = $1
        WHERE id = $2
      `
      const result = await this.postgresService.query(query, [new Date(), id])
      return result.rowCount > 0
    } catch (error) {
      console.error('Erro ao restaurar usuário:', error)
      return false
    }
  }

  /**
   * 🏛️ Obter departamentos do usuário
   */
  async getUserDepartments(userId: string): Promise<string[]> {
    try {
      const query = `
        SELECT id_departamento 
        FROM usuario_departamentos 
        WHERE id_usuario = $1
      `
      const result = await this.postgresService.query(query, [userId])
      return result.rows.map(row => row.id_departamento)
    } catch (error) {
      console.error('Erro ao obter departamentos do usuário:', error)
      return []
    }
  }

  /**
   * ➕ Adicionar usuário a departamento
   */
  async addUserToDepartment(userId: string, departmentId: string): Promise<void> {
    try {
      const query = `
        INSERT INTO usuario_departamentos (id_usuario, id_departamento, created_at)
        VALUES ($1, $2, $3)
        ON CONFLICT (id_usuario, id_departamento) DO NOTHING
      `
      await this.postgresService.query(query, [userId, departmentId, new Date()])
    } catch (error) {
      console.error('Erro ao adicionar usuário ao departamento:', error)
    }
  }

  /**
   * ➖ Remover usuário de departamento
   */
  async removeUserFromDepartment(userId: string, departmentId: string): Promise<void> {
    try {
      const query = `
        DELETE FROM usuario_departamentos 
        WHERE id_usuario = $1 AND id_departamento = $2
      `
      await this.postgresService.query(query, [userId, departmentId])
    } catch (error) {
      console.error('Erro ao remover usuário do departamento:', error)
    }
  }

  /**
   * 📧 Verificar se email já está em uso
   */
  async isEmailTaken(email: string, excludeId?: string): Promise<boolean> {
    try {
      const normalizedEmail = UserRepositoryUtils.normalizeEmail(email)
      let query = `
        SELECT COUNT(*) as total FROM usuarios 
        WHERE LOWER(email) = $1 AND excluido = false
      `
      const values = [normalizedEmail]
      
      if (excludeId) {
        query += ` AND id != $2`
        values.push(excludeId)
      }
      
      const result = await this.postgresService.query(query, values)
      return parseInt(result.rows[0].total) > 0
    } catch (error) {
      console.error('Erro ao verificar email:', error)
      return false
    }
  }

  /**
   * 🆔 Verificar se CPF já está em uso
   */
  async isCPFTaken(cpf: string, excludeId?: string): Promise<boolean> {
    try {
      const sanitizedCPF = UserRepositoryUtils.sanitizeCPF(cpf)
      let query = `
        SELECT COUNT(*) as total FROM usuarios 
        WHERE cpf = $1 AND excluido = false
      `
      const values = [sanitizedCPF]
      
      if (excludeId) {
        query += ` AND id != $2`
        values.push(excludeId)
      }
      
      const result = await this.postgresService.query(query, values)
      return parseInt(result.rows[0].total) > 0
    } catch (error) {
      console.error('Erro ao verificar CPF:', error)
      return false
    }
  }

  /**
   * 🕒 Atualizar expiração de senha
   */
  async updatePasswordExpiry(id: string, expiryDate: Date): Promise<void> {
    try {
      const query = `
        UPDATE usuarios 
        SET data_validade_senha = $1, updated_at = $2
        WHERE id = $3
      `
      await this.postgresService.query(query, [expiryDate, new Date(), id])
    } catch (error) {
      console.error('Erro ao atualizar expiração da senha:', error)
    }
  }

  /**
   * 📊 Obter estatísticas dos usuários
   */
  async getStats(): Promise<UserStats> {
    try {
      const query = `
        SELECT 
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE conta_ativa = true AND excluido = false) as ativos,
          COUNT(*) FILTER (WHERE servidor = true) as servidores
        FROM usuarios
        WHERE excluido = false
      `
      
      const result = await this.postgresService.query(query)
      const row = result.rows[0]
      
      return {
        total: parseInt(row.total),
        ativos: parseInt(row.ativos),
        servidores: parseInt(row.servidores),
        por_secretaria: {} as any, // Implementar quando houver mapeamento
        por_role: {} as any // Implementar quando houver mapeamento
      }
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error)
      return {
        total: 0,
        ativos: 0,
        servidores: 0,
        por_secretaria: {} as any,
        por_role: {} as any
      }
    }
  }

  /**
   * 🔄 Mapear linha do banco para objeto User
   */
  private mapDatabaseRowToUser(row: any): User {
    return {
      id: row.id?.toString() || '',
      nome: row.nome || '',
      email: row.email || null,
      cpf: row.cpf || '',
      senha: row.senha || null,
      nova_senha: row.nova_senha || null,
      redefinir_senha: Boolean(row.redefinir_senha),
      conta_ativa: Boolean(row.conta_ativa),
      data_validade_nova_senha: row.data_validade_nova_senha ? new Date(row.data_validade_nova_senha) : null,
      data_validade_senha: row.data_validade_senha ? new Date(row.data_validade_senha) : null,
      foto_perfil: row.foto_perfil || null,
      servidor: Boolean(row.servidor),
      created_at: row.created_at ? new Date(row.created_at) : undefined,
      updated_at: row.updated_at ? new Date(row.updated_at) : undefined,
      facebook_id: row.facebook_id || null,
      google_id: row.google_id || null,
      segunda_senha: row.segunda_senha || null,
      cadastro_manual: Boolean(row.cadastro_manual),
      excluido: Boolean(row.excluido),
      registro_medico: row.registro_medico || null,
      registro_medico_uf: row.registro_medico_uf || null,
      visualizar_processo_sigiloso: Boolean(row.visualizar_processo_sigiloso),
      codigo_cns: row.codigo_cns || null
    }
  }

  /**
   * 🧪 Testar conectividade com banco
   */
  async testConnection(): Promise<boolean> {
    try {
      return await this.postgresService.testConnection()
    } catch (error) {
      console.error('Erro ao testar conexão:', error)
      return false
    }
  }
}