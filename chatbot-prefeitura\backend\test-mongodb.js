const mongoose = require('mongoose');

async function testMongoDB() {
    try {
        console.log('🔄 Testando conexão MongoDB...');

        // Conectar ao MongoDB - banco específico de Valparaíso
        await mongoose.connect('********************************************************************');

        console.log('✅ Conexão MongoDB bem-sucedida!');

        // Verificar coleções no banco de Valparaíso
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log(`📊 Total de coleções no banco ${mongoose.connection.name}: ${collections.length}`);

        if (collections.length > 0) {
            collections.forEach(col => {
                console.log(`  - ${col.name}`);
            });
        } else {
            console.log('📭 Nenhuma coleção encontrada');
        }

        // Verificar status da conexão
        console.log(`\n🔗 Status da conexão: ${mongoose.connection.readyState === 1 ? 'Conectado' : 'Desconectado'}`);
        console.log(`🏠 Host: ${mongoose.connection.host}`);
        console.log(`🚪 Porta: ${mongoose.connection.port}`);
        console.log(`🗄️ Database atual: ${mongoose.connection.name}`);

    } catch (error) {
        console.error('❌ Erro na conexão MongoDB:', error.message);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Conexão MongoDB fechada');
    }
}

testMongoDB();
