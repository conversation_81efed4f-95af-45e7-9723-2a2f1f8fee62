// Script simples para listar tabelas do PostgreSQL
// Usando apenas libs básicas do Node.js + pg (se disponível)

console.log('🔍 Testando conexão com PostgreSQL da prefeitura...\n');

// Tentar importar pg
let pgClient = null;
try {
  const { Client } = require('pg');
  pgClient = Client;
  console.log('✅ Driver PostgreSQL encontrado');
} catch (e) {
  console.log('❌ Driver PostgreSQL não disponível:', e.message);
  console.log('\n💡 Para instalar: npm install pg');
  process.exit(1);
}

async function exploreDatabase() {
  const client = new pgClient({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'pv_valparaiso',
    ssl: false,
    connectionTimeoutMillis: 5000,
  });

  try {
    console.log('🔌 Conectando...');
    await client.connect();
    console.log('✅ Conectado com sucesso!\n');

    // Listar tabelas básico
    console.log('📋 TABELAS ENCONTRADAS:\n');
    
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);

    console.log(`Total: ${result.rows.length} tabelas\n`);

    // Para cada tabela, mostrar informações básicas
    for (let i = 0; i < Math.min(result.rows.length, 20); i++) {
      const table = result.rows[i].table_name;
      console.log(`${(i + 1).toString().padStart(2)}. ${table}`);
      
      try {
        // Contar registros
        const countResult = await client.query(`SELECT COUNT(*) FROM "${table}"`);
        const count = countResult.rows[0].count;
        console.log(`    📊 Registros: ${parseInt(count).toLocaleString()}`);
        
        // Mostrar algumas colunas
        const colsResult = await client.query(`
          SELECT column_name, data_type 
          FROM information_schema.columns 
          WHERE table_name = $1 
          ORDER BY ordinal_position 
          LIMIT 5
        `, [table]);
        
        console.log(`    📝 Primeiras colunas:`);
        colsResult.rows.forEach(col => {
          console.log(`       - ${col.column_name} (${col.data_type})`);
        });
        
      } catch (e) {
        console.log(`    ⚠️  Erro ao analisar: ${e.message}`);
      }
      
      console.log('');
    }

    if (result.rows.length > 20) {
      console.log(`... e mais ${result.rows.length - 20} tabelas\n`);
    }

    // Buscar tabelas relevantes
    console.log('🎯 TABELAS RELEVANTES POR CATEGORIA:\n');
    
    const categories = {
      'Processos/Documentos': ['processo', 'protocolo', 'documento', 'solicitacao'],
      'Pessoas': ['usuario', 'pessoa', 'cidadao', 'funcionario', 'servidor'],
      'Organização': ['secretaria', 'departamento', 'setor', 'orgao'],
      'Financeiro': ['orcamento', 'despesa', 'receita', 'pagamento']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      const matches = result.rows.filter(row => 
        keywords.some(keyword => row.table_name.toLowerCase().includes(keyword))
      );
      
      if (matches.length > 0) {
        console.log(`📂 ${category}:`);
        for (const match of matches) {
          try {
            const countResult = await client.query(`SELECT COUNT(*) FROM "${match.table_name}"`);
            const count = parseInt(countResult.rows[0].count);
            console.log(`   - ${match.table_name} (${count.toLocaleString()} registros)`);
          } catch (e) {
            console.log(`   - ${match.table_name} (erro ao contar)`);
          }
        }
        console.log('');
      }
    }

  } catch (error) {
    console.error('❌ Erro:', error.message);
    if (error.code) {
      console.error('   Código:', error.code);
    }
  } finally {
    await client.end();
    console.log('✅ Conexão encerrada.');
  }
}

// Executar
exploreDatabase().catch(console.error);