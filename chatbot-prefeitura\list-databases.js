// Script para listar bancos disponíveis no PostgreSQL
const { Client } = require('pg');

async function listDatabases() {
  console.log('🔍 Conectando ao PostgreSQL para listar bancos disponíveis...\n');

  // Tentar conectar ao banco postgres (padrão)
  const client = new Client({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'postgres', // banco padrão
    ssl: false,
    connectionTimeoutMillis: 5000,
  });

  try {
    await client.connect();
    console.log('✅ Conectado ao PostgreSQL!\n');

    // Listar todos os bancos
    console.log('📋 BANCOS DE DADOS DISPONÍVEIS:\n');
    
    const result = await client.query(`
      SELECT datname as database_name, 
             pg_size_pretty(pg_database_size(datname)) as size
      FROM pg_database
      WHERE datistemplate = false
      ORDER BY datname;
    `);

    result.rows.forEach((row, i) => {
      console.log(`${(i + 1).toString().padStart(2)}. ${row.database_name} (${row.size})`);
    });

    console.log(`\nTotal: ${result.rows.length} bancos encontrados\n`);

    // Tentar conectar a bancos que podem ser da prefeitura
    const possibleDbs = result.rows
      .map(row => row.database_name)
      .filter(name => 
        name.includes('valparaiso') || 
        name.includes('prefeitura') || 
        name.includes('pv') ||
        !['postgres', 'template0', 'template1'].includes(name)
      );

    if (possibleDbs.length > 0) {
      console.log('🎯 BANCOS POSSIVELMENTE RELACIONADOS À PREFEITURA:');
      possibleDbs.forEach(db => console.log(`   - ${db}`));
      
      // Tentar explorar o primeiro banco relevante
      if (possibleDbs.length > 0) {
        await explorePrefeituraDb(possibleDbs[0]);
      }
    } else {
      console.log('⚠️ Nenhum banco específico da prefeitura encontrado');
      console.log('💡 Tentando explorar o banco "postgres" padrão...');
      await exploreDefaultDb(client);
    }

  } catch (error) {
    console.error('❌ Erro:', error.message);
    if (error.code) {
      console.error('   Código:', error.code);
    }
  } finally {
    await client.end();
  }
}

async function explorePrefeituraDb(dbName) {
  console.log(`\n🔍 EXPLORANDO BANCO: ${dbName}`);
  console.log('=' * 50);
  
  const client = new Client({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: dbName,
    ssl: false,
    connectionTimeoutMillis: 5000,
  });

  try {
    await client.connect();
    console.log(`✅ Conectado ao banco ${dbName}!\n`);

    // Listar tabelas
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);

    console.log(`📋 TABELAS NO BANCO ${dbName}:\n`);
    
    if (result.rows.length === 0) {
      console.log('   Nenhuma tabela encontrada no esquema public');
    } else {
      result.rows.forEach((row, i) => {
        console.log(`${(i + 1).toString().padStart(2)}. ${row.table_name}`);
      });

      // Mostrar detalhes das primeiras 5 tabelas
      console.log('\n📊 DETALHES DAS PRIMEIRAS TABELAS:\n');
      
      for (let i = 0; i < Math.min(5, result.rows.length); i++) {
        const table = result.rows[i].table_name;
        console.log(`🔹 ${table}:`);
        
        try {
          // Contar registros
          const countResult = await client.query(`SELECT COUNT(*) FROM "${table}"`);
          console.log(`   📈 Registros: ${parseInt(countResult.rows[0].count).toLocaleString()}`);
          
          // Listar colunas
          const colsResult = await client.query(`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = $1 
            ORDER BY ordinal_position 
            LIMIT 5
          `, [table]);
          
          console.log('   📝 Colunas:');
          colsResult.rows.forEach(col => {
            console.log(`      - ${col.column_name} (${col.data_type})`);
          });
          
        } catch (e) {
          console.log(`   ⚠️ Erro ao analisar: ${e.message}`);
        }
        console.log('');
      }
    }

  } catch (error) {
    console.error(`❌ Erro ao explorar ${dbName}:`, error.message);
  } finally {
    await client.end();
  }
}

async function exploreDefaultDb(client) {
  try {
    // Ver se há tabelas no banco postgres padrão
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);

    if (result.rows.length > 0) {
      console.log('\n📋 TABELAS NO BANCO POSTGRES (padrão):');
      result.rows.forEach((row, i) => {
        console.log(`${(i + 1).toString().padStart(2)}. ${row.table_name}`);
      });
    } else {
      console.log('\n📋 Nenhuma tabela encontrada no banco postgres padrão');
    }

  } catch (error) {
    console.error('❌ Erro ao explorar banco padrão:', error.message);
  }
}

// Executar
listDatabases().catch(console.error);