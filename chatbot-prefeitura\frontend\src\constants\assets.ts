/**
 * 📁 Sistema de Gerenciamento de Assets
 * Centraliza todos os caminhos de imagens, logos e ícones
 */

// Base path para assets
const ASSETS_BASE = '/assets'

export const ASSETS = {
  // 🏢 Logos da Empresa Desenvolvedora
  logos: {
    empresa: {
      principal: `${ASSETS_BASE}/logos/empresa/logo-principal.svg`,
      horizontal: `${ASSETS_BASE}/logos/empresa/logo-horizontal.svg`,
      icone: `${ASSETS_BASE}/logos/empresa/logo-icone.svg`,
      branca: `${ASSETS_BASE}/logos/empresa/logo-branca.svg`,
      monocromatica: `${ASSETS_BASE}/logos/empresa/logo-monocromatica.svg`,
    },
    prefeitura: {
      brasao: `${ASSETS_BASE}/logos/prefeitura/brasao-valparaiso.svg`,
      logo: `${ASSETS_BASE}/logos/prefeitura/logo-prefeitura.svg`,
      marcaCompleta: `${ASSETS_BASE}/logos/prefeitura/marca-completa.svg`,
    }
  },

  // 🖼️ Imagens de Background
  backgrounds: {
    heroValparaiso: `${ASSETS_BASE}/images/backgrounds/hero-valparaiso.jpg`,
    patternInstitutional: `${ASSETS_BASE}/images/backgrounds/pattern-institutional.svg`,
    gradientOverlay: `${ASSETS_BASE}/images/backgrounds/gradient-overlay.svg`,
  },

  // 🏛️ Ícones das Secretarias
  secretarias: {
    administracao: `${ASSETS_BASE}/images/icons/secretarias/administracao.svg`,
    financas: `${ASSETS_BASE}/images/icons/secretarias/financas.svg`,
    saude: `${ASSETS_BASE}/images/icons/secretarias/saude.svg`,
    educacao: `${ASSETS_BASE}/images/icons/secretarias/educacao.svg`,
    obras: `${ASSETS_BASE}/images/icons/secretarias/obras.svg`,
    social: `${ASSETS_BASE}/images/icons/secretarias/social.svg`,
    meioAmbiente: `${ASSETS_BASE}/images/icons/secretarias/meio-ambiente.svg`,
  },

  // 🔧 Ícones da Interface
  interface: {
    chatBubble: `${ASSETS_BASE}/images/icons/interface/chat-bubble.svg`,
    loadingSpinner: `${ASSETS_BASE}/images/icons/interface/loading-spinner.svg`,
    notification: `${ASSETS_BASE}/images/icons/interface/notification.svg`,
  },

  // 🎨 Ilustrações
  illustrations: {
    emptyState: `${ASSETS_BASE}/images/illustrations/empty-state.svg`,
    errorPage: `${ASSETS_BASE}/images/illustrations/error-page.svg`,
    successFeedback: `${ASSETS_BASE}/images/illustrations/success-feedback.svg`,
  },

  // 📱 Favicons
  favicons: {
    main: `${ASSETS_BASE}/favicon/favicon.ico`,
    png16: `${ASSETS_BASE}/favicon/favicon-16x16.png`,
    png32: `${ASSETS_BASE}/favicon/favicon-32x32.png`,
    appleTouchIcon: `${ASSETS_BASE}/favicon/apple-touch-icon.png`,
  }
} as const

// 📋 Mapeamento de Secretarias para Ícones
export const SECRETARIA_ICONS = {
  'administracao': ASSETS.secretarias.administracao,
  'financas': ASSETS.secretarias.financas,
  'saude': ASSETS.secretarias.saude,
  'educacao': ASSETS.secretarias.educacao,
  'obras': ASSETS.secretarias.obras,
  'social': ASSETS.secretarias.social,
  'meio_ambiente': ASSETS.secretarias.meioAmbiente,
} as const

// 🎯 Tipos TypeScript para melhor DX
export type AssetPath = typeof ASSETS[keyof typeof ASSETS]
export type SecretariaKey = keyof typeof SECRETARIA_ICONS
export type LogoVariant = keyof typeof ASSETS.logos.empresa

// 🔍 Função para verificar se asset existe
export const assetExists = (path: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(true)
    img.onerror = () => resolve(false)
    img.src = path
  })
}

// 🚀 Função para precarregar assets críticos
export const preloadCriticalAssets = () => {
  const criticalAssets = [
    ASSETS.logos.empresa.principal,
    ASSETS.logos.prefeitura.brasao,
    ASSETS.interface.chatBubble,
    ASSETS.interface.loadingSpinner,
  ]

  criticalAssets.forEach(assetPath => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = assetPath
    link.as = 'image'
    document.head.appendChild(link)
  })
}