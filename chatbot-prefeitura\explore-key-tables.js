// Script para explorar tabelas-chave para o chatbot
const { Client } = require('pg');

async function exploreKeyTables() {
  console.log('🎯 EXPLORANDO TABELAS-CHAVE PARA O CHATBOT\n');
  
  const client = new Client({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'prefeituravirtual',
    ssl: false
  });

  try {
    await client.connect();
    console.log('✅ Conectado ao banco prefeituravirtual!\n');

    // Tabelas prioritárias para o chatbot
    const keyTables = [
      // Usuários e Departamentos
      'usuarios',
      'departamentos', 
      'usuario_departamentos',
      'funcoes',
      
      // Processos e Protocolo
      'protocolo_virtual_processos',
      'protocolo_virtual_movimentacao',
      'protocolo_virtual_interessados',
      'protocolo_virtual_situacaos',
      'protocolo_virtual_assuntos',
      
      // Requisições/Solicitações
      'requisicao',
      'requisicao_servico',
      'requisicao_status',
      'requisicao_movimentacao',
      
      // Serviços Online
      'servicos',
      'servicos_online_servicos',
      'servicos_onlines',
      
      // Saúde (principais)
      'saude_segurados',
      'saude_agendamentos_itens',
      'saude_consultas',
      
      // Formulários
      'formulario',
      'formulario_campos'
    ];

    for (const tableName of keyTables) {
      await analyzeTable(client, tableName);
    }

    // Análise especial: encontrar tabelas com mais registros
    console.log('\n📊 TOP 15 TABELAS COM MAIS REGISTROS:\n');
    await findBiggestTables(client);

    // Buscar relacionamentos importantes
    console.log('\n🔗 RELACIONAMENTOS CRÍTICOS:\n');
    await findKeyRelationships(client);

  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    await client.end();
    console.log('\n✅ Análise concluída.');
  }
}

async function analyzeTable(client, tableName) {
  console.log(`🔍 Analisando: ${tableName}`);
  
  try {
    // Verificar se tabela existe
    const existsResult = await client.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = $1 AND table_schema = 'public'
      )
    `, [tableName]);

    if (!existsResult.rows[0].exists) {
      console.log(`   ⚠️ Tabela não encontrada\n`);
      return;
    }

    // Contar registros
    const countResult = await client.query(`SELECT COUNT(*) FROM "${tableName}"`);
    const count = parseInt(countResult.rows[0].count);
    console.log(`   📊 Registros: ${count.toLocaleString()}`);

    if (count === 0) {
      console.log(`   📝 Tabela vazia\n`);
      return;
    }

    // Estrutura das colunas
    const columnsResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = $1
      ORDER BY ordinal_position
    `, [tableName]);

    console.log(`   📝 Colunas (${columnsResult.rows.length}):`);
    
    // Mostrar colunas importantes
    const importantColumns = columnsResult.rows.filter(col => {
      const name = col.column_name.toLowerCase();
      return (
        name.includes('id') || 
        name.includes('nome') || 
        name.includes('email') ||
        name.includes('status') ||
        name.includes('situacao') ||
        name.includes('created') ||
        name.includes('updated') ||
        name === 'numero' ||
        name === 'protocolo' ||
        name === 'cpf' ||
        name === 'departamento'
      );
    });

    importantColumns.slice(0, 8).forEach(col => {
      const nullable = col.is_nullable === 'YES' ? '?' : '';
      console.log(`      - ${col.column_name} (${col.data_type})${nullable}`);
    });

    // Se for uma tabela com muitos dados, mostrar amostra
    if (count > 10 && count < 100000) {
      try {
        const sampleResult = await client.query(`SELECT * FROM "${tableName}" LIMIT 3`);
        if (sampleResult.rows.length > 0) {
          console.log(`   🔍 Campos com dados (primeira amostra):`);
          const firstRow = sampleResult.rows[0];
          Object.keys(firstRow).slice(0, 6).forEach(key => {
            const value = firstRow[key];
            if (value !== null && value !== '') {
              const displayValue = value.toString().length > 30 
                ? value.toString().substring(0, 30) + '...' 
                : value.toString();
              console.log(`      ${key}: ${displayValue}`);
            }
          });
        }
      } catch (e) {
        // Ignorar erros de amostra
      }
    }

    console.log('');
    
  } catch (error) {
    console.log(`   ❌ Erro ao analisar: ${error.message}\n`);
  }
}

async function findBiggestTables(client) {
  const tablesQuery = `
    SELECT table_name
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    ORDER BY table_name
  `;
  
  const tablesResult = await client.query(tablesQuery);
  const tableData = [];

  // Pegar contagem de cada tabela (limitando para não sobrecarregar)
  for (const table of tablesResult.rows.slice(0, 100)) {
    try {
      const countResult = await client.query(`SELECT COUNT(*) FROM "${table.table_name}"`);
      const count = parseInt(countResult.rows[0].count);
      if (count > 0) {
        tableData.push({ name: table.table_name, count });
      }
    } catch (e) {
      // Ignorar tabelas com problema
    }
  }

  // Ordenar por quantidade e mostrar top 15
  tableData.sort((a, b) => b.count - a.count);
  
  tableData.slice(0, 15).forEach((table, i) => {
    console.log(`${(i + 1).toString().padStart(2)}. ${table.name.padEnd(40)} ${table.count.toLocaleString().padStart(10)} registros`);
  });
}

async function findKeyRelationships(client) {
  const fkQuery = `
    SELECT
      tc.table_name as origem,
      kcu.column_name as coluna_origem,
      ccu.table_name as destino,
      ccu.column_name as coluna_destino
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
    WHERE constraint_type = 'FOREIGN KEY' 
      AND tc.table_schema = 'public'
    ORDER BY tc.table_name
    LIMIT 20
  `;

  const fkResult = await client.query(fkQuery);
  
  if (fkResult.rows.length > 0) {
    fkResult.rows.forEach(fk => {
      console.log(`   ${fk.origem}.${fk.coluna_origem} → ${fk.destino}.${fk.coluna_destino}`);
    });
  } else {
    console.log('   Nenhuma FK explícita encontrada (podem haver relacionamentos por convenção)');
  }
}

// Executar análise
exploreKeyTables().catch(console.error);