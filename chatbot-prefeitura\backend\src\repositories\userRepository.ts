/**
 * 🏗️ User Repository - Interface Comum
 * Repository pattern para abstração do acesso a dados de usuários
 */

import { User, AuthUser, CreateUserDto, UpdateUserDto, UserSearchParams, UserStats, SecretariaKey, UserRole } from '../types/user.types'

/**
 * 📋 Interface principal do repositório de usuários
 */
export interface IUserRepository {
  // 🔍 Busca de usuários
  findById(id: string): Promise<User | null>
  findByEmail(email: string): Promise<User | null>
  findByCPF(cpf: string): Promise<User | null>
  findByEmailOrCPF(identifier: string): Promise<User | null>
  
  // 🔐 Autenticação
  validatePassword(user: User, password: string): Promise<boolean>
  hashPassword(password: string): Promise<string>
  
  // 📊 Listagem e busca
  findMany(params: UserSearchParams): Promise<User[]>
  count(params: Omit<UserSearchParams, 'limit' | 'offset'>): Promise<number>
  
  // ✏️ Manipulação de dados
  create(userData: CreateUserDto): Promise<User>
  update(id: string, userData: UpdateUserDto): Promise<User | null>
  updateLastLogin(id: string): Promise<void>
  
  // 🗑️ Remoção
  softDelete(id: string): Promise<boolean>
  restore(id: string): Promise<boolean>
  
  // 📈 Estatísticas
  getStats(): Promise<UserStats>
  
  // 🏛️ Departamentos/Secretarias
  getUserDepartments(userId: string): Promise<string[]>
  addUserToDepartment(userId: string, departmentId: string): Promise<void>
  removeUserFromDepartment(userId: string, departmentId: string): Promise<void>
  
  // 🔧 Utilitários
  exists(id: string): Promise<boolean>
  isEmailTaken(email: string, excludeId?: string): Promise<boolean>
  isCPFTaken(cpf: string, excludeId?: string): Promise<boolean>
  
  // 🧹 Manutenção
  cleanupExpiredPasswordResets(): Promise<number>
  updatePasswordExpiry(id: string, expiryDate: Date): Promise<void>
}

/**
 * 🎯 Repository base abstrato com implementações comuns
 */
export abstract class BaseUserRepository implements IUserRepository {
  
  /**
   * 🔑 Gera hash da senha usando bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    const bcrypt = require('bcryptjs')
    const saltRounds = 10
    return bcrypt.hash(password, saltRounds)
  }

  /**
   * ✅ Valida senha comparando com hash
   */
  async validatePassword(user: User, password: string): Promise<boolean> {
    if (!user.senha) return false
    
    const bcrypt = require('bcryptjs')
    return bcrypt.compare(password, user.senha)
  }

  /**
   * 🔍 Busca por email OU CPF (flexível)
   */
  async findByEmailOrCPF(identifier: string): Promise<User | null> {
    // Tentar primeiro como email
    if (identifier.includes('@')) {
      return this.findByEmail(identifier)
    }
    
    // Senão, tratar como CPF
    return this.findByCPF(identifier)
  }

  /**
   * ✅ Verifica se usuário existe
   */
  async exists(id: string): Promise<boolean> {
    const user = await this.findById(id)
    return user !== null
  }

  /**
   * 📊 Implementação padrão de estatísticas (pode ser sobrescrita)
   */
  async getStats(): Promise<UserStats> {
    const allUsers = await this.findMany({})
    
    const stats: UserStats = {
      total: allUsers.length,
      ativos: allUsers.filter(u => u.conta_ativa && !u.excluido).length,
      servidores: allUsers.filter(u => u.servidor).length,
      por_secretaria: {} as Record<SecretariaKey, number>,
      por_role: {} as Record<UserRole, number>
    }
    
    // Inicializar contadores
    const secretarias: SecretariaKey[] = ['administracao', 'financas', 'saude', 'educacao', 'obras', 'social', 'meio_ambiente']
    const roles: UserRole[] = ['admin', 'gestor', 'operador', 'consulta']
    
    secretarias.forEach(s => stats.por_secretaria[s] = 0)
    roles.forEach(r => stats.por_role[r] = 0)
    
    return stats
  }

  /**
   * 🧹 Limpeza padrão de redefinições de senha expiradas
   */
  async cleanupExpiredPasswordResets(): Promise<number> {
    // Implementação padrão - pode ser sobrescrita
    return 0
  }

  // 🔤 Métodos abstratos que devem ser implementados pelas classes filhas
  abstract findById(id: string): Promise<User | null>
  abstract findByEmail(email: string): Promise<User | null>
  abstract findByCPF(cpf: string): Promise<User | null>
  abstract findMany(params: UserSearchParams): Promise<User[]>
  abstract count(params: Omit<UserSearchParams, 'limit' | 'offset'>): Promise<number>
  abstract create(userData: CreateUserDto): Promise<User>
  abstract update(id: string, userData: UpdateUserDto): Promise<User | null>
  abstract updateLastLogin(id: string): Promise<void>
  abstract softDelete(id: string): Promise<boolean>
  abstract restore(id: string): Promise<boolean>
  abstract getUserDepartments(userId: string): Promise<string[]>
  abstract addUserToDepartment(userId: string, departmentId: string): Promise<void>
  abstract removeUserFromDepartment(userId: string, departmentId: string): Promise<void>
  abstract isEmailTaken(email: string, excludeId?: string): Promise<boolean>
  abstract isCPFTaken(cpf: string, excludeId?: string): Promise<boolean>
  abstract updatePasswordExpiry(id: string, expiryDate: Date): Promise<void>
}

/**
 * 🔧 Utilitários para repositórios
 */
export class UserRepositoryUtils {
  
  /**
   * 🧹 Sanitiza CPF removendo formatação
   */
  static sanitizeCPF(cpf: string): string {
    return cpf.replace(/\D/g, '')
  }
  
  /**
   * 📧 Normaliza email para lowercase
   */
  static normalizeEmail(email: string): string {
    return email.trim().toLowerCase()
  }
  
  /**
   * ✅ Valida formato de CPF
   */
  static isValidCPF(cpf: string): boolean {
    const cleanCPF = this.sanitizeCPF(cpf)
    
    if (cleanCPF.length !== 11) return false
    if (/^(\d)\1+$/.test(cleanCPF)) return false // Todos os dígitos iguais
    
    // Validação do algoritmo do CPF
    let sum = 0
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cleanCPF.charAt(i)) * (10 - i)
    }
    let digit1 = (sum * 10) % 11
    if (digit1 === 10) digit1 = 0
    
    if (digit1 !== parseInt(cleanCPF.charAt(9))) return false
    
    sum = 0
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cleanCPF.charAt(i)) * (11 - i)
    }
    let digit2 = (sum * 10) % 11
    if (digit2 === 10) digit2 = 0
    
    return digit2 === parseInt(cleanCPF.charAt(10))
  }
  
  /**
   * ✅ Valida formato de email
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
  
  /**
   * 🎲 Gera ID único
   */
  static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
  
  /**
   * 🔍 Constrói query WHERE para busca
   */
  static buildSearchQuery(params: UserSearchParams): { conditions: string[], values: any[] } {
    const conditions: string[] = []
    const values: any[] = []
    let valueIndex = 1
    
    if (params.nome) {
      conditions.push(`nome ILIKE $${valueIndex}`)
      values.push(`%${params.nome}%`)
      valueIndex++
    }
    
    if (params.email) {
      conditions.push(`email ILIKE $${valueIndex}`)
      values.push(`%${params.email}%`)
      valueIndex++
    }
    
    if (params.cpf) {
      conditions.push(`cpf = $${valueIndex}`)
      values.push(this.sanitizeCPF(params.cpf))
      valueIndex++
    }
    
    if (params.conta_ativa !== undefined) {
      conditions.push(`conta_ativa = $${valueIndex}`)
      values.push(params.conta_ativa)
      valueIndex++
    }
    
    if (params.servidor !== undefined) {
      conditions.push(`servidor = $${valueIndex}`)
      values.push(params.servidor)
      valueIndex++
    }
    
    // Sempre filtrar não excluídos por padrão
    conditions.push(`excluido = false`)
    
    return { conditions, values }
  }
}