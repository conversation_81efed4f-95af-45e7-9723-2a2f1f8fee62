# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Claude Behavior Guidelines

- Sempre falar em PT-BR

## Development Commands

### Root Level Commands
```bash
# Start both backend and frontend in development mode
npm run dev

# Build both applications for production
npm run build

# Start both applications in production mode
npm start

# Run tests for both backend and frontend
npm run test
```

### Backend Commands (from /chatbot-prefeitura/backend/)
```bash
# Development with hot reload
npm run dev

# Production build
npm run build

# Start production server
npm start

# Run tests
npm run test

# Lint TypeScript files
npm run lint

# Type checking without emitting files
npm run typecheck

# Test DeepSeek AI integration
npm run test:deepseek

# Test database connections
npm run test:db

# Map database schema
npm run map:db
```

### Frontend Commands (from /chatbot-prefeitura/frontend/)
```bash
# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start

# Lint Next.js application
npm run lint

# Type checking
npm run typecheck
```

## Project Architecture

### Monorepo Structure
This is a workspace-based monorepo with separate backend and frontend applications:

- **Root**: Workspace configuration and shared scripts
- **Backend**: Node.js/Express API with TypeScript
- **Frontend**: Next.js React application with TypeScript

### Backend Architecture
```
backend/src/
├── index.ts              # Express server entry point
├── controllers/          # Route handlers (auth, chat, dashboard)
├── services/            
│   ├── ai/              # DeepSeek AI integration
│   ├── chatbot/         # Chat and conversation services
│   ├── database/        # PostgreSQL and MongoDB services
│   └── auth/            # Authentication services
├── middleware/          # Express middleware (auth, validation, errors)
├── routes/              # API route definitions
├── config/              # Application constants and configuration
└── utils/               # Utilities (logging, helpers)
```

### Key Technologies

**Backend Stack:**
- **Runtime**: Node.js 18+
- **Framework**: Express.js with TypeScript
- **Databases**: PostgreSQL (Prisma ORM) + MongoDB (Mongoose)
- **AI Integration**: DeepSeek API for chatbot intelligence
- **Security**: JWT authentication, bcrypt, helmet, CORS, rate limiting
- **Logging**: Winston + Morgan
- **Validation**: Zod schemas

**Frontend Stack:**
- **Framework**: Next.js 14 with React 18
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand + React Query
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Radix UI primitives

### Database Architecture

**PostgreSQL (prefeituravirtual database)**:
- Primary application data
- User authentication and permissions
- Municipal system data (409 tables)
- Connection: `*************:5411`

**MongoDB (chatbot_prefeitura database)**:
- Chat conversations and history
- Audit logs and user activity tracking
- AI interaction data
- Connection: `*************:2711`

### Authentication System
- JWT-based authentication with role-based access control
- Users organized by municipal departments (secretarias)
- Mock users available for development in `auth.controller.ts`
- Permissions system with different user roles (admin, gestor, operador, consulta)

### AI Integration
- DeepSeek API integration for intelligent chatbot responses
- Department-specific prompts and context
- SQL query generation for database interactions
- Conversation history management with context awareness

### Environment Configuration
Essential environment variables are defined in `backend/.env`:
- `DEEPSEEK_API_KEY`: AI service integration
- `DATABASE_URL`: PostgreSQL connection
- `MONGODB_URI`: MongoDB connection
- `JWT_SECRET`: Authentication token signing
- Rate limiting and security configurations

### Development Setup Notes
- Use `npm install` at root to install all workspace dependencies
- Backend runs on port 3001, frontend on port 3000
- MCP (Model Context Protocol) servers available for direct database access during development
- Comprehensive logging system with Winston for debugging and monitoring
- Mock authentication data available for development testing

### Municipal Departments (Secretarias)
The system supports multiple municipal departments:
- Administração (Administration)
- Finanças (Finance)
- Saúde (Health)
- Educação (Education)
- Obras e Urbanismo (Public Works)
- Assistência Social (Social Services)
- Meio Ambiente (Environment)

Each department has customized chatbot prompts and specific database access patterns.