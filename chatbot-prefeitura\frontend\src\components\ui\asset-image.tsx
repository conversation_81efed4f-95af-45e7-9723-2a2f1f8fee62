'use client'

import Image from 'next/image'
import { useState } from 'react'
import { cn } from '@/lib/utils'

interface AssetImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  fallback?: string
  priority?: boolean
  fill?: boolean
  sizes?: string
  quality?: number
}

/**
 * 🖼️ Componente AssetImage Otimizado
 * 
 * Wrapper around Next.js Image com:
 * - Fallback automático para imagens quebradas
 * - Loading states elegantes
 * - Otimizações de performance
 * - TypeScript support completo
 */
export function AssetImage({
  src,
  alt,
  width,
  height,
  className,
  fallback = '/assets/images/illustrations/empty-state.svg',
  priority = false,
  fill = false,
  sizes,
  quality = 75,
  ...props
}: AssetImageProps) {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const handleError = () => {
    setImageError(true)
    setIsLoading(false)
  }

  const handleLoad = () => {
    setIsLoading(false)
  }

  // Se erro, usa fallback
  const imageSrc = imageError ? fallback : src

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Loading skeleton */}
      {isLoading && (
        <div 
          className="absolute inset-0 bg-muted animate-pulse rounded-md"
          style={{ width: width || '100%', height: height || '100%' }}
        />
      )}
      
      {/* Imagem principal */}
      <Image
        src={imageSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        sizes={sizes}
        quality={quality}
        priority={priority}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100',
          className
        )}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
      
      {/* Indicador de erro (opcional) */}
      {imageError && (
        <div className="absolute top-2 right-2">
          <div className="w-2 h-2 bg-destructive rounded-full opacity-50" />
        </div>
      )}
    </div>
  )
}

/**
 * 🏢 Componente especializado para logos
 */
interface LogoImageProps extends Omit<AssetImageProps, 'alt'> {
  variant?: 'principal' | 'horizontal' | 'icone' | 'branca' | 'monocromatica'
  type?: 'empresa' | 'prefeitura'
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

export function LogoImage({
  variant = 'principal',
  type = 'empresa', 
  size = 'md',
  className,
  ...props
}: LogoImageProps) {
  // Importar ASSETS aqui para evitar circular dependency
  const { ASSETS } = require('@/constants/assets')
  
  const sizeClasses = {
    sm: 'h-8 w-auto',
    md: 'h-12 w-auto', 
    lg: 'h-16 w-auto',
    xl: 'h-20 w-auto'
  }

  const logoSrc = type === 'empresa' 
    ? ASSETS.logos.empresa[variant]
    : ASSETS.logos.prefeitura.brasao

  return (
    <AssetImage
      src={logoSrc}
      alt={`Logo ${type === 'empresa' ? 'da Empresa' : 'da Prefeitura'}`}
      className={cn(sizeClasses[size], 'object-contain', className)}
      priority={variant === 'principal'} // Logo principal tem prioridade
      {...props}
    />
  )
}

/**
 * 🏛️ Componente para ícones de secretarias
 */
interface SecretariaIconProps extends Omit<AssetImageProps, 'src' | 'alt'> {
  secretaria: 'administracao' | 'financas' | 'saude' | 'educacao' | 'obras' | 'social' | 'meio_ambiente'
  size?: number
}

export function SecretariaIcon({
  secretaria,
  size = 24,
  className,
  ...props
}: SecretariaIconProps) {
  const { SECRETARIA_ICONS } = require('@/constants/assets')
  
  const secretariaNames = {
    'administracao': 'Administração',
    'financas': 'Finanças',
    'saude': 'Saúde', 
    'educacao': 'Educação',
    'obras': 'Obras e Urbanismo',
    'social': 'Assistência Social',
    'meio_ambiente': 'Meio Ambiente'
  }

  return (
    <AssetImage
      src={SECRETARIA_ICONS[secretaria]}
      alt={`Ícone ${secretariaNames[secretaria]}`}
      width={size}
      height={size}
      className={cn('flex-shrink-0', className)}
      {...props}
    />
  )
}