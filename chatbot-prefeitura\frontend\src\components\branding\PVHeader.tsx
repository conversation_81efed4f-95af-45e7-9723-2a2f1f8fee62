'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { LogoPV } from './LogoPV'
import { BrandButton } from './BrandButton'
import { 
  Bell,
  Settings,
  User,
  Menu,
  Search,
  LogOut,
  Moon,
  Sun
} from 'lucide-react'

interface PVHeaderProps {
  className?: string
  /** T<PERSON>tulo da página atual */
  title?: string
  /** Subtítulo ou breadcrumb */
  subtitle?: string
  /** Se deve mostrar a logo */
  showLogo?: boolean
  /** Se deve mostrar o menu hamburger (mobile) */
  showMenuToggle?: boolean
  /** Se deve mostrar a barra de busca */
  showSearch?: boolean
  /** Se deve mostrar notificações */
  showNotifications?: boolean
  /** Callback para toggle do menu */
  onMenuToggle?: () => void
  /** Callback para busca */
  onSearch?: (query: string) => void
  /** Callback para logout */
  onLogout?: () => void
  /** Dados do usuário */
  user?: {
    nome: string
    secretaria: string
    avatar?: string
  }
}

/**
 * 🎯 Header Principal do Sistema PV
 * 
 * Header responsivo com branding PV, navegação e funcionalidades.
 * Inclui logo, busca, notificações, perfil do usuário e tema.
 */
export function PVHeader({
  className,
  title,
  subtitle,
  showLogo = true,
  showMenuToggle = true,
  showSearch = true,
  showNotifications = true,
  onMenuToggle,
  onSearch,
  onLogout,
  user,
  ...props
}: PVHeaderProps) {
  const [searchQuery, setSearchQuery] = React.useState('')
  const [isSearchExpanded, setIsSearchExpanded] = React.useState(false)
  const [isDark, setIsDark] = React.useState(false)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch?.(searchQuery)
  }

  const toggleTheme = () => {
    setIsDark(!isDark)
    document.documentElement.classList.toggle('dark')
  }

  return (
    <header 
      className={cn(
        'sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
        'shadow-pv',
        className
      )}
      {...props}
    >
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Left Side - Logo e Menu */}
        <div className="flex items-center gap-4">
          {showMenuToggle && (
            <BrandButton
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={onMenuToggle}
              aria-label="Abrir menu"
            >
              <Menu className="h-5 w-5" />
            </BrandButton>
          )}
          
          {showLogo && (
            <div className="flex items-center gap-3">
              <LogoPV 
                size="sm" 
                priority 
                className="hover:scale-105 transition-transform cursor-pointer"
              />
              
              {(title || subtitle) && (
                <div className="hidden sm:block">
                  {title && (
                    <h1 className="text-lg font-semibold text-foreground">
                      {title}
                    </h1>
                  )}
                  {subtitle && (
                    <p className="text-sm text-muted-foreground">
                      {subtitle}
                    </p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Center - Search (Desktop) */}
        {showSearch && (
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <form onSubmit={handleSearch} className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="search"
                placeholder="Buscar processos, documentos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={cn(
                  'w-full pl-10 pr-4 py-2 rounded-md border border-input',
                  'bg-background text-foreground placeholder:text-muted-foreground',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent',
                  'transition-all duration-200'
                )}
              />
            </form>
          </div>
        )}

        {/* Right Side - Actions */}
        <div className="flex items-center gap-2">
          {/* Search Mobile */}
          {showSearch && (
            <BrandButton
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsSearchExpanded(!isSearchExpanded)}
              aria-label="Buscar"
            >
              <Search className="h-5 w-5" />
            </BrandButton>
          )}

          {/* Theme Toggle */}
          <BrandButton
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            aria-label="Alternar tema"
          >
            {isDark ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </BrandButton>

          {/* Notifications */}
          {showNotifications && (
            <BrandButton
              variant="ghost"
              size="icon"
              className="relative"
              aria-label="Notificações"
            >
              <Bell className="h-5 w-5" />
              {/* Badge de notificação */}
              <span className="absolute -top-1 -right-1 h-4 w-4 bg-pv-gold text-xs font-bold text-pv-gray-dark rounded-full flex items-center justify-center">
                3
              </span>
            </BrandButton>
          )}

          {/* Settings */}
          <BrandButton
            variant="ghost"
            size="icon"
            aria-label="Configurações"
          >
            <Settings className="h-5 w-5" />
          </BrandButton>

          {/* User Menu */}
          {user && (
            <div className="flex items-center gap-3">
              {/* User Info (Desktop) */}
              <div className="hidden lg:block text-right">
                <p className="text-sm font-medium text-foreground">
                  {user.nome}
                </p>
                <p className="text-xs text-muted-foreground">
                  {user.secretaria}
                </p>
              </div>

              {/* Avatar */}
              <BrandButton
                variant="ghost"
                size="icon"
                className="rounded-full"
                aria-label="Menu do usuário"
              >
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.nome}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <User className="h-5 w-5" />
                )}
              </BrandButton>
            </div>
          )}

          {/* Logout */}
          {onLogout && (
            <BrandButton
              variant="ghost"
              size="icon"
              onClick={onLogout}
              className="text-destructive hover:text-destructive"
              aria-label="Sair"
            >
              <LogOut className="h-5 w-5" />
            </BrandButton>
          )}
        </div>
      </div>

      {/* Expanded Search (Mobile) */}
      {showSearch && isSearchExpanded && (
        <div className="md:hidden border-t bg-background p-4 animate-slide-up">
          <form onSubmit={handleSearch} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="search"
              placeholder="Buscar processos, documentos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={cn(
                'w-full pl-10 pr-4 py-2 rounded-md border border-input',
                'bg-background text-foreground placeholder:text-muted-foreground',
                'focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent'
              )}
              autoFocus
            />
          </form>
        </div>
      )}
    </header>
  )
}

/**
 * 🎨 Header Simples (para páginas específicas)
 */
interface SimpleHeaderProps {
  title: string
  subtitle?: string
  backUrl?: string
  actions?: React.ReactNode
  className?: string
}

export function SimpleHeader({
  title,
  subtitle,
  backUrl,
  actions,
  className
}: SimpleHeaderProps) {
  return (
    <header className={cn(
      'border-b bg-background px-6 py-4',
      'shadow-pv',
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {backUrl && (
            <BrandButton
              variant="ghost"
              size="icon"
              asChild
            >
              <a href={backUrl}>
                <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="m15 18-6-6 6-6"/>
                </svg>
              </a>
            </BrandButton>
          )}
          
          <div>
            <h1 className="text-xl font-semibold text-foreground">
              {title}
            </h1>
            {subtitle && (
              <p className="text-sm text-muted-foreground mt-1">
                {subtitle}
              </p>
            )}
          </div>
        </div>
        
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
    </header>
  )
}