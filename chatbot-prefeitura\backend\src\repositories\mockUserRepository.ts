/**
 * 🎭 Mock User Repository
 * Implementação em memória baseada na estrutura real da tabela usuarios
 */

import { BaseUserRepository } from './userRepository'
import { User, CreateUserDto, UpdateUserDto, UserSearchParams, SecretariaKey, UserRole, UserRepositoryUtils } from '../types/user.types'

/**
 * 🎭 Repositório Mock com dados realistas
 */
export class MockUserRepository extends BaseUserRepository {
  private users: User[] = []
  
  constructor() {
    super()
    this.initializeMockData()
  }

  /**
   * 🏗️ Inicializa dados mock baseados na estrutura real
   */
  private initializeMockData(): void {
    const now = new Date()
    
    this.users = [
      // Administrador Geral
      {
        id: '1',
        nome: '<PERSON>',
        email: '<EMAIL>',
        cpf: '12345678901',
        senha: '$2a$10$xZ1kZfL3.kO2xM6kG6lH9uHxKkP5oVqFbVZx4xK5I9dHJKGzVqX2G', // admin123
        nova_senha: null,
        redefinir_senha: false,
        conta_ativa: true,
        data_validade_nova_senha: null,
        data_validade_senha: null,
        foto_perfil: null,
        servidor: true,
        created_at: now,
        updated_at: now,
        facebook_id: null,
        google_id: null,
        segunda_senha: null,
        cadastro_manual: true,
        excluido: false,
        registro_medico: null,
        registro_medico_uf: null,
        visualizar_processo_sigiloso: true,
        codigo_cns: null
      },
      
      // Gestor da Saúde
      {
        id: '2',
        nome: 'Maria Oliveira Souza',
        email: '<EMAIL>',
        cpf: '98765432109',
        senha: '$2a$10$yZ2kZfL4.kO3xM7kG7lH0uHxKkP6oVqFbVZx5xK6I0dHJKGzVqX3H', // gestor123
        nova_senha: null,
        redefinir_senha: false,
        conta_ativa: true,
        data_validade_nova_senha: null,
        data_validade_senha: null,
        foto_perfil: null,
        servidor: true,
        created_at: now,
        updated_at: now,
        facebook_id: null,
        google_id: null,
        segunda_senha: null,
        cadastro_manual: true,
        excluido: false,
        registro_medico: 'CRM12345',
        registro_medico_uf: 'GO',
        visualizar_processo_sigiloso: false,
        codigo_cns: '123456789012345'
      },
      
      // Operador Educação
      {
        id: '3',
        nome: 'Carlos Eduardo Lima',
        email: '<EMAIL>',
        cpf: '11122233344',
        senha: '$2a$10$zZ3kZfL5.kO4xM8kG8lH1uHxKkP7oVqFbVZx6xK7I1dHJKGzVqX4I', // operador123
        nova_senha: null,
        redefinir_senha: false,
        conta_ativa: true,
        data_validade_nova_senha: null,
        data_validade_senha: null,
        foto_perfil: null,
        servidor: true,
        created_at: now,
        updated_at: now,
        facebook_id: null,
        google_id: null,
        segunda_senha: null,
        cadastro_manual: true,
        excluido: false,
        registro_medico: null,
        registro_medico_uf: null,
        visualizar_processo_sigiloso: false,
        codigo_cns: null
      },
      
      // Usuário Finanças
      {
        id: '4',
        nome: 'Ana Paula Costa',
        email: '<EMAIL>',
        cpf: '55566677788',
        senha: '$2a$10$aA4kZfL6.kO5xM9kG9lH2uHxKkP8oVqFbVZx7xK8I2dHJKGzVqX5J', // consulta123
        nova_senha: null,
        redefinir_senha: false,
        conta_ativa: true,
        data_validade_nova_senha: null,
        data_validade_senha: null,
        foto_perfil: null,
        servidor: true,
        created_at: now,
        updated_at: now,
        facebook_id: null,
        google_id: null,
        segunda_senha: null,
        cadastro_manual: true,
        excluido: false,
        registro_medico: null,
        registro_medico_uf: null,
        visualizar_processo_sigiloso: false,
        codigo_cns: null
      },
      
      // Servidor Obras
      {
        id: '5',
        nome: 'Roberto Almeida Santos',
        email: '<EMAIL>',
        cpf: '99988877766',
        senha: '$2a$10$bB5kZfL7.kO6xM0kG0lH3uHxKkP9oVqFbVZx8xK9I3dHJKGzVqX6K', // servidor123
        nova_senha: null,
        redefinir_senha: false,
        conta_ativa: true,
        data_validade_nova_senha: null,
        data_validade_senha: null,
        foto_perfil: null,
        servidor: true,
        created_at: now,
        updated_at: now,
        facebook_id: null,
        google_id: null,
        segunda_senha: null,
        cadastro_manual: true,
        excluido: false,
        registro_medico: null,
        registro_medico_uf: null,
        visualizar_processo_sigiloso: false,
        codigo_cns: null
      }
    ]
  }

  /**
   * 🔍 Buscar usuário por ID
   */
  async findById(id: string): Promise<User | null> {
    const user = this.users.find(u => u.id === id && !u.excluido)
    return user || null
  }

  /**
   * 📧 Buscar usuário por email
   */
  async findByEmail(email: string): Promise<User | null> {
    const normalizedEmail = UserRepositoryUtils.normalizeEmail(email)
    const user = this.users.find(u => 
      u.email?.toLowerCase() === normalizedEmail && !u.excluido
    )
    return user || null
  }

  /**
   * 🆔 Buscar usuário por CPF
   */
  async findByCPF(cpf: string): Promise<User | null> {
    const sanitizedCPF = UserRepositoryUtils.sanitizeCPF(cpf)
    const user = this.users.find(u => 
      UserRepositoryUtils.sanitizeCPF(u.cpf) === sanitizedCPF && !u.excluido
    )
    return user || null
  }

  /**
   * 📋 Buscar múltiplos usuários com filtros
   */
  async findMany(params: UserSearchParams): Promise<User[]> {
    let filtered = this.users.filter(u => !u.excluido)
    
    // Aplicar filtros
    if (params.nome) {
      filtered = filtered.filter(u => 
        u.nome.toLowerCase().includes(params.nome!.toLowerCase())
      )
    }
    
    if (params.email) {
      filtered = filtered.filter(u => 
        u.email?.toLowerCase().includes(params.email!.toLowerCase())
      )
    }
    
    if (params.cpf) {
      const sanitizedCPF = UserRepositoryUtils.sanitizeCPF(params.cpf)
      filtered = filtered.filter(u => 
        UserRepositoryUtils.sanitizeCPF(u.cpf) === sanitizedCPF
      )
    }
    
    if (params.conta_ativa !== undefined) {
      filtered = filtered.filter(u => u.conta_ativa === params.conta_ativa)
    }
    
    if (params.servidor !== undefined) {
      filtered = filtered.filter(u => u.servidor === params.servidor)
    }
    
    // Paginação
    const offset = params.offset || 0
    const limit = params.limit || 50
    
    return filtered.slice(offset, offset + limit)
  }

  /**
   * 🔢 Contar usuários com filtros
   */
  async count(params: Omit<UserSearchParams, 'limit' | 'offset'>): Promise<number> {
    const filtered = await this.findMany({ ...params, limit: undefined, offset: undefined })
    return filtered.length
  }

  /**
   * ➕ Criar novo usuário
   */
  async create(userData: CreateUserDto): Promise<User> {
    const now = new Date()
    const hashedPassword = await this.hashPassword(userData.senha)
    
    const newUser: User = {
      id: UserRepositoryUtils.generateId(),
      nome: userData.nome,
      email: userData.email || null,
      cpf: UserRepositoryUtils.sanitizeCPF(userData.cpf),
      senha: hashedPassword,
      nova_senha: null,
      redefinir_senha: false,
      conta_ativa: true,
      data_validade_nova_senha: null,
      data_validade_senha: null,
      foto_perfil: userData.foto_perfil || null,
      servidor: userData.servidor || false,
      created_at: now,
      updated_at: now,
      facebook_id: null,
      google_id: null,
      segunda_senha: null,
      cadastro_manual: true,
      excluido: false,
      registro_medico: null,
      registro_medico_uf: null,
      visualizar_processo_sigiloso: false,
      codigo_cns: null
    }
    
    this.users.push(newUser)
    return newUser
  }

  /**
   * ✏️ Atualizar usuário
   */
  async update(id: string, userData: UpdateUserDto): Promise<User | null> {
    const userIndex = this.users.findIndex(u => u.id === id && !u.excluido)
    
    if (userIndex === -1) return null
    
    const user = this.users[userIndex]
    const updatedUser: User = {
      ...user,
      ...userData,
      updated_at: new Date()
    }
    
    // Se houver nova senha, fazer hash
    if (userData.nova_senha) {
      updatedUser.senha = await this.hashPassword(userData.nova_senha)
      updatedUser.nova_senha = null
      updatedUser.redefinir_senha = false
    }
    
    this.users[userIndex] = updatedUser
    return updatedUser
  }

  /**
   * 🕒 Atualizar último login
   */
  async updateLastLogin(id: string): Promise<void> {
    const userIndex = this.users.findIndex(u => u.id === id)
    if (userIndex !== -1) {
      this.users[userIndex].updated_at = new Date()
    }
  }

  /**
   * 🗑️ Soft delete
   */
  async softDelete(id: string): Promise<boolean> {
    const userIndex = this.users.findIndex(u => u.id === id)
    
    if (userIndex === -1) return false
    
    this.users[userIndex].excluido = true
    this.users[userIndex].conta_ativa = false
    this.users[userIndex].updated_at = new Date()
    
    return true
  }

  /**
   * ♻️ Restaurar usuário
   */
  async restore(id: string): Promise<boolean> {
    const userIndex = this.users.findIndex(u => u.id === id)
    
    if (userIndex === -1) return false
    
    this.users[userIndex].excluido = false
    this.users[userIndex].conta_ativa = true
    this.users[userIndex].updated_at = new Date()
    
    return true
  }

  /**
   * 🏛️ Obter departamentos do usuário (mock simples)
   */
  async getUserDepartments(userId: string): Promise<string[]> {
    // Mock simples - em produção viria da tabela usuario_departamentos
    return ['1'] // Departamento padrão
  }

  /**
   * ➕ Adicionar usuário a departamento
   */
  async addUserToDepartment(userId: string, departmentId: string): Promise<void> {
    // Mock - não faz nada por enquanto
  }

  /**
   * ➖ Remover usuário de departamento
   */
  async removeUserFromDepartment(userId: string, departmentId: string): Promise<void> {
    // Mock - não faz nada por enquanto
  }

  /**
   * 📧 Verificar se email já está em uso
   */
  async isEmailTaken(email: string, excludeId?: string): Promise<boolean> {
    const normalizedEmail = UserRepositoryUtils.normalizeEmail(email)
    return this.users.some(u => 
      u.email?.toLowerCase() === normalizedEmail && 
      !u.excluido && 
      u.id !== excludeId
    )
  }

  /**
   * 🆔 Verificar se CPF já está em uso
   */
  async isCPFTaken(cpf: string, excludeId?: string): Promise<boolean> {
    const sanitizedCPF = UserRepositoryUtils.sanitizeCPF(cpf)
    return this.users.some(u => 
      UserRepositoryUtils.sanitizeCPF(u.cpf) === sanitizedCPF && 
      !u.excluido && 
      u.id !== excludeId
    )
  }

  /**
   * 🕒 Atualizar expiração de senha
   */
  async updatePasswordExpiry(id: string, expiryDate: Date): Promise<void> {
    const userIndex = this.users.findIndex(u => u.id === id)
    if (userIndex !== -1) {
      this.users[userIndex].data_validade_senha = expiryDate
      this.users[userIndex].updated_at = new Date()
    }
  }
}

/**
 * 🎭 Mapeamento de usuários mock para secretarias
 * Usado pelo sistema de autenticação para determinar secretaria/role
 */
export const MOCK_USER_MAPPINGS: Record<string, { secretaria: SecretariaKey, role: UserRole }> = {
  '1': { secretaria: 'administracao', role: 'admin' },     // João Silva
  '2': { secretaria: 'saude', role: 'gestor' },            // Maria Oliveira
  '3': { secretaria: 'educacao', role: 'operador' },       // Carlos Eduardo
  '4': { secretaria: 'financas', role: 'consulta' },       // Ana Paula
  '5': { secretaria: 'obras', role: 'operador' },          // Roberto Almeida
}

/**
 * 🔑 Senhas dos usuários mock (para documentação)
 * Em desenvolvimento, as senhas são:
 * - admin123 (João Silva)
 * - gestor123 (Maria Oliveira)
 * - operador123 (Carlos Eduardo)
 * - consulta123 (Ana Paula)
 * - servidor123 (Roberto Almeida)
 */