# 📁 Estrutura de Assets - Chatbot Prefeitura

## 🎯 Organização

### **📊 Logos**
```
logos/
├── empresa/                    # Logo da empresa desenvolvedora
│   ├── logo-principal.*        # Logo principal (SVG/PNG)
│   ├── logo-horizontal.*       # Versão horizontal
│   ├── logo-icone.*           # Apenas ícone/símbolo
│   ├── logo-branca.*          # Versão para fundos escuros
│   └── logo-monocromatica.*   # Versão em uma cor
└── prefeitura/                # Logos oficiais da prefeitura
    ├── brasao-valparaiso.*    # Brasão oficial
    ├── logo-prefeitura.*      # Logo da prefeitura
    └── marca-completa.*       # Marca completa
```

### **🖼️ Imagens**
```
images/
├── backgrounds/               # Imagens de fundo
│   ├── hero-valparaiso.jpg   # Foto da cidade para hero section
│   ├── pattern-institutional.svg # Padrões governamentais
│   └── gradient-overlay.svg   # Gradientes para sobreposições
├── icons/
│   ├── secretarias/          # Ícones para cada departamento
│   │   ├── administracao.svg
│   │   ├── financas.svg
│   │   ├── saude.svg
│   │   ├── educacao.svg
│   │   ├── obras.svg
│   │   ├── social.svg
│   │   └── meio-ambiente.svg
│   └── interface/            # Ícones da interface
│       ├── chat-bubble.svg
│       ├── loading-spinner.svg
│       └── notification.svg
└── illustrations/            # Ilustrações customizadas
    ├── empty-state.svg       # Estados vazios
    ├── error-page.svg        # Páginas de erro
    └── success-feedback.svg  # Feedback de sucesso
```

### **🔖 Favicons**
```
favicon/
├── favicon.ico              # Favicon principal
├── favicon-16x16.png       # 16x16 pixels
├── favicon-32x32.png       # 32x32 pixels
├── apple-touch-icon.png    # iOS home screen
└── manifest.json           # PWA manifest
```

## 🎨 Processo de Extração de Cores

1. **Upload da Logo**: Adicione a logo principal da empresa em `logos/empresa/logo-principal.*`
2. **Extração Automática**: O sistema extrairá automaticamente as cores dominantes
3. **Geração do Tema**: CSS Variables serão geradas baseadas na identidade visual
4. **Aplicação**: Cores aplicadas automaticamente em todos os componentes

## 📋 Checklist de Assets

### **Logos Essenciais:**
- [ ] Logo principal da empresa (formato SVG preferível)
- [ ] Versão horizontal da logo
- [ ] Ícone/símbolo da empresa
- [ ] Versão branca para fundos escuros
- [ ] Brasão de Valparaíso de Goiás
- [ ] Logo oficial da Prefeitura

### **Imagens de Interface:**
- [ ] Foto panorâmica de Valparaíso (hero section)
- [ ] Ícones das 7 secretarias
- [ ] Ilustrações para estados vazios
- [ ] Padrões decorativos institucionais

### **Favicons:**
- [ ] Favicon em múltiplos tamanhos
- [ ] Apple touch icon
- [ ] Manifest para PWA

## 🚀 Como Usar

Após adicionar os assets, utilize o sistema de importação:

```tsx
import { AssetImage } from '@/components/ui/asset-image'
import { ASSETS } from '@/constants/assets'

// Componente otimizado
<AssetImage 
  src={ASSETS.logos.empresa.principal} 
  alt="Logo da Empresa" 
  width={200} 
  height={60} 
/>

// Acesso direto
<img src="/assets/logos/empresa/logo-principal.svg" alt="Logo" />
```

## 🎯 Próximo Passo

**Faça o upload da logo principal da empresa** em `logos/empresa/logo-principal.*` para que eu possa extrair automaticamente a paleta de cores e criar o tema visual personalizado!