/**
 * 🎨 Paleta de Cores - Prefeitura Virtual (PV)
 * Baseada na logo PVAlinhada.png
 */

// 🔵 Cores Principais da Logo PV
export const PV_COLORS = {
  // A<PERSON><PERSON> da Logo (Gradiente do V)
  royal: '#1e40af',      // Azul Royal - Base do V
  primary: '#3b82f6',    // Azul Médio - Gradiente central
  cyan: '#0ea5e9',       // Azul Ciano - VIRTUAL
  
  // Dourado/Amarelo (Pixels superiores)
  gold: '#fbbf24',       // Dourado principal
  goldDark: '#f59e0b',   // Dourado mais escuro
  
  // Neutros (Texto PREFEITURA)
  gray: '#374151',       // Cinza escuro principal
  grayLight: '#6b7280',  // Cinza médio
  grayDark: '#1f2937',   // Cinza muito escuro
  
  // Brancos e fundos
  white: '#ffffff',
  grayBg: '#f9fafb',     // Background claro
  grayBorder: '#e5e7eb', // Bordas sutis
} as const

// 🎯 Gradientes da Logo
export const PV_GRADIENTS = {
  // Gradiente principal do V
  primary: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #0ea5e9 100%)',
  
  // Gradiente reverso
  primaryReverse: 'linear-gradient(315deg, #0ea5e9 0%, #3b82f6 50%, #1e40af 100%)',
  
  // Gradiente dourado
  gold: 'linear-gradient(45deg, #fbbf24 0%, #f59e0b 100%)',
  
  // Gradiente sutil para backgrounds
  subtle: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
  
  // Gradiente para cards
  card: 'linear-gradient(135deg, rgba(30, 64, 175, 0.03) 0%, rgba(14, 165, 233, 0.03) 100%)',
} as const

// 🎨 Sistema de Cores Semânticas
export const PV_SEMANTIC = {
  // Estados do sistema
  success: '#10b981',    // Verde sucesso
  warning: '#f59e0b',    // Amarelo/dourado (já da paleta)
  error: '#ef4444',      // Vermelho erro
  info: '#0ea5e9',       // Azul ciano (já da paleta)
  
  // Tons de azul para diferentes contextos
  primary: {
    50: '#eff6ff',
    100: '#dbeafe', 
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',  // Base
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',  // Royal
    900: '#1e3a8a',
    950: '#172554',
  },
  
  // Tons de ciano
  cyan: {
    50: '#ecfeff',
    100: '#cffafe',
    200: '#a5f3fc', 
    300: '#67e8f9',
    400: '#22d3ee',
    500: '#0ea5e9',  // Base
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#083344',
  },
  
  // Tons de dourado
  gold: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d', 
    400: '#fbbf24',  // Base
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  }
} as const

// 🌙 Modo Escuro
export const PV_DARK = {
  background: '#0f172a',     // Fundo principal escuro
  surface: '#1e293b',       // Surface/cards
  border: '#334155',        // Bordas
  text: '#f1f5f9',         // Texto principal
  textMuted: '#cbd5e1',     // Texto secundário
  
  // Cores ajustadas para modo escuro
  primary: '#60a5fa',       // Azul mais claro
  cyan: '#22d3ee',          // Ciano mais vibrante
  gold: '#fcd34d',          // Dourado mais claro
} as const

// 💫 Efeitos Visuais
export const PV_EFFECTS = {
  // Sombras com cores da marca
  shadow: {
    sm: '0 1px 2px 0 rgba(30, 64, 175, 0.05)',
    md: '0 4px 6px -1px rgba(30, 64, 175, 0.1), 0 2px 4px -1px rgba(30, 64, 175, 0.06)',
    lg: '0 10px 15px -3px rgba(30, 64, 175, 0.1), 0 4px 6px -2px rgba(30, 64, 175, 0.05)',
    xl: '0 20px 25px -5px rgba(30, 64, 175, 0.1), 0 10px 10px -5px rgba(30, 64, 175, 0.04)',
  },
  
  // Glows para elementos especiais
  glow: {
    primary: '0 0 20px rgba(59, 130, 246, 0.3)',
    cyan: '0 0 20px rgba(14, 165, 233, 0.3)',
    gold: '0 0 20px rgba(251, 191, 36, 0.3)',
  },
  
  // Bordas com gradiente
  borderGradient: 'linear-gradient(90deg, #1e40af, #0ea5e9)',
} as const

// 🎯 Função para acessar cores facilmente
export function getPVColor(color: keyof typeof PV_COLORS): string {
  return PV_COLORS[color]
}

// 🌈 Função para gerar variações de uma cor
export function getPVColorVariation(baseColor: string, opacity: number): string {
  // Converte hex para rgba com opacity
  const hex = baseColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`
}

// 🔄 Função para alternar entre claro e escuro
export function getPVColorForTheme(lightColor: string, darkColor: string, isDark: boolean): string {
  return isDark ? darkColor : lightColor
}