const mongoose = require('mongoose');

async function testMongoDB() {
    try {
        console.log('🔄 Testando conexão MongoDB...');
        
        // Conectar ao MongoDB - testando sem especificar database
        await mongoose.connect('**********************************************/');
        
        console.log('✅ Conexão MongoDB bem-sucedida!');
        
        // Listar coleções
        const collections = await mongoose.connection.db.listCollections().toArray();
        
        console.log(`📊 Total de coleções: ${collections.length}`);
        
        if (collections.length > 0) {
            console.log('📋 Coleções encontradas:');
            collections.forEach(col => {
                console.log(`  - ${col.name}`);
            });
        } else {
            console.log('📭 Nenhuma coleção encontrada (banco vazio)');
        }
        
        // Verificar status da conexão
        console.log(`🔗 Status da conexão: ${mongoose.connection.readyState === 1 ? 'Conectado' : 'Desconectado'}`);
        console.log(`🏠 Host: ${mongoose.connection.host}`);
        console.log(`🚪 Porta: ${mongoose.connection.port}`);
        console.log(`🗄️ Database: ${mongoose.connection.name}`);
        
    } catch (error) {
        console.error('❌ Erro na conexão MongoDB:', error.message);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Conexão MongoDB fechada');
    }
}

testMongoDB();
