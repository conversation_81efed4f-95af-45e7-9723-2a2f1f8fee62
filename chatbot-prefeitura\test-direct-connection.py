#!/usr/bin/env python3
"""
Script para testar conexão direta com PostgreSQL da prefeitura
e listar todas as tabelas com suas estruturas
"""

import psycopg2
import sys

def connect_and_explore():
    print("🔍 Explorando banco PostgreSQL da Prefeitura de Valparaíso...")
    print("=" * 60)
    
    try:
        # Conectar ao banco
        conn = psycopg2.connect(
            host="*************",
            port=5411,
            user="otto",  
            password="otto",
            database="pv_valparaiso"
        )
        
        cursor = conn.cursor()
        print("✅ Conexão estabelecida com sucesso!\n")
        
        # 1. Listar todas as tabelas
        print("📋 LISTANDO TODAS AS TABELAS:")
        print("-" * 40)
        
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
              AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        print(f"Total de tabelas encontradas: {len(tables)}\n")
        
        # Para cada tabela, obter informações detalhadas
        for table_tuple in tables:
            table_name = table_tuple[0]
            print(f"📊 Tabela: {table_name}")
            
            # Contar registros
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}";')
                count = cursor.fetchone()[0]
                print(f"   📈 Registros: {count:,}")
            except Exception as e:
                print(f"   📈 Registros: Erro ao contar - {e}")
            
            # Listar colunas
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = %s
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns = cursor.fetchall()
            print(f"   📝 Colunas ({len(columns)}):")
            
            for col in columns[:8]:  # Mostrar apenas primeiras 8 colunas
                col_name, data_type, nullable, default = col
                nullable_str = "NULL" if nullable == "YES" else "NOT NULL"
                default_str = f", default: {default}" if default else ""
                print(f"      - {col_name} ({data_type}, {nullable_str}{default_str})")
            
            if len(columns) > 8:
                print(f"      ... e mais {len(columns) - 8} colunas")
            
            print()
        
        # 2. Buscar tabelas por relevância
        print("\n🎯 ANÁLISE POR RELEVÂNCIA:")
        print("-" * 40)
        
        keywords = {
            'Processos': ['processo', 'protocolo', 'documento', 'solicitacao'],
            'Pessoas': ['usuario', 'pessoa', 'cidadao', 'funcionario', 'servidor'],
            'Organização': ['secretaria', 'departamento', 'setor', 'orgao'],
            'Financeiro': ['orcamento', 'despesa', 'receita', 'pagamento', 'financ']
        }
        
        all_table_names = [t[0] for t in tables]
        
        for category, terms in keywords.items():
            matching = []
            for table in all_table_names:
                if any(term in table.lower() for term in terms):
                    matching.append(table)
            
            if matching:
                print(f"\n🔍 {category}:")
                for table in matching:
                    cursor.execute(f'SELECT COUNT(*) FROM "{table}";')
                    count = cursor.fetchone()[0]
                    print(f"   - {table} ({count:,} registros)")
        
        # 3. Relacionamentos
        print(f"\n🔗 RELACIONAMENTOS (Foreign Keys):")
        print("-" * 40)
        
        cursor.execute("""
            SELECT
                tc.table_name as origem,
                kcu.column_name as coluna_origem,
                ccu.table_name as destino,
                ccu.column_name as coluna_destino
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
            WHERE constraint_type = 'FOREIGN KEY' 
              AND tc.table_schema = 'public'
            ORDER BY tc.table_name;
        """)
        
        fks = cursor.fetchall()
        
        if fks:
            for fk in fks:
                print(f"   {fk[0]}.{fk[1]} → {fk[2]}.{fk[3]}")
        else:
            print("   Nenhum relacionamento FK encontrado (ou não visível)")
        
        # 4. Tabelas mais relevantes para o chatbot
        print(f"\n⭐ RECOMENDAÇÕES PARA O CHATBOT:")
        print("-" * 40)
        
        # Encontrar as maiores tabelas
        big_tables = []
        for table_tuple in tables:
            table_name = table_tuple[0]
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}";')
                count = cursor.fetchone()[0]
                if count > 0:
                    big_tables.append((table_name, count))
            except:
                pass
        
        big_tables.sort(key=lambda x: x[1], reverse=True)
        
        print("📊 Top 10 tabelas com mais dados:")
        for i, (table, count) in enumerate(big_tables[:10]):
            print(f"   {i+1:2d}. {table:<30} {count:>10,} registros")
        
        print("\n💡 PRÓXIMOS PASSOS RECOMENDADOS:")
        print("   1. Focar nas tabelas com mais registros")
        print("   2. Analisar estrutura das tabelas de processos/protocolos")  
        print("   3. Mapear relacionamentos entre entidades principais")
        print("   4. Criar queries específicas por secretaria")
        
    except psycopg2.Error as e:
        print(f"❌ Erro de PostgreSQL: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()
            print("\n✅ Conexão encerrada.")
    
    return True

if __name__ == "__main__":
    success = connect_and_explore()
    sys.exit(0 if success else 1)