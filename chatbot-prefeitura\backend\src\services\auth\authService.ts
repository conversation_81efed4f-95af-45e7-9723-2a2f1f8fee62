/**
 * 🔐 AuthService Híbrido
 * Orquestrador inteligente que detecta automaticamente qual repositório usar
 */

import jwt from 'jsonwebtoken'
import { IUserRepository } from '../../repositories/userRepository'
import { MockUserRepository, MOCK_USER_MAPPINGS } from '../../repositories/mockUserRepository'
import { RealUserRepository } from '../../repositories/realUserRepository'
import { 
  AuthUser, 
  AuthResponse, 
  LoginDto, 
  JwtPayload, 
  SecretariaKey, 
  UserRole,
  User,
  userToAuthUser,
  canUserLogin
} from '../../types/user.types'
import { logger } from '../../utils/logger'

/**
 * 🎯 Configurações do AuthService
 */
interface AuthServiceConfig {
  jwtSecret: string
  jwtExpiresIn: string
  useRealUsers?: boolean
  autoDetectRealUsers?: boolean
}

/**
 * 📊 Resultado de tentativa de login
 */
interface LoginAttemptResult {
  success: boolean
  user?: AuthUser
  token?: string
  expiresIn?: number
  error?: string
  reason?: string
}

/**
 * 🔐 Serviço de Autenticação Híbrido
 */
export class AuthService {
  private userRepository: IUserRepository
  private config: AuthServiceConfig
  private isUsingRealUsers: boolean = false

  constructor(config?: Partial<AuthServiceConfig>) {
    this.config = {
      jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key',
      jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
      useRealUsers: process.env.USE_REAL_USERS === 'true',
      autoDetectRealUsers: process.env.AUTO_DETECT_REAL_USERS !== 'false',
      ...config
    }

    this.initializeRepository()
  }

  /**
   * 🏗️ Inicializa o repositório apropriado
   */
  private async initializeRepository(): Promise<void> {
    try {
      // Se forçado a usar real users
      if (this.config.useRealUsers) {
        await this.tryInitializeRealRepository()
        return
      }

      // Auto-detecção
      if (this.config.autoDetectRealUsers) {
        const realInitialized = await this.tryInitializeRealRepository()
        if (!realInitialized) {
          this.initializeMockRepository()
        }
      } else {
        this.initializeMockRepository()
      }
    } catch (error) {
      logger.error('Erro na inicialização do repositório:', error)
      this.initializeMockRepository()
    }
  }

  /**
   * 🗄️ Tenta inicializar repositório real
   */
  private async tryInitializeRealRepository(): Promise<boolean> {
    try {
      const realRepo = new RealUserRepository()
      
      // Testar conectividade
      const connected = await realRepo.testConnection()
      if (!connected) {
        logger.warn('Conexão com PostgreSQL falhou - usando mock')
        return false
      }

      // Verificar se tem dados
      const userCount = await realRepo.count({})
      if (userCount === 0) {
        logger.warn('Nenhum usuário encontrado no banco real - usando mock')
        return false
      }

      this.userRepository = realRepo
      this.isUsingRealUsers = true
      logger.info(`✅ AuthService inicializado com dados REAIS (${userCount} usuários)`)
      return true

    } catch (error) {
      logger.error('Erro ao inicializar repositório real:', error)
      return false
    }
  }

  /**
   * 🎭 Inicializa repositório mock
   */
  private initializeMockRepository(): void {
    this.userRepository = new MockUserRepository()
    this.isUsingRealUsers = false
    logger.info('✅ AuthService inicializado com dados MOCK')
  }

  /**
   * 🔐 Login principal
   */
  async login(loginData: LoginDto): Promise<LoginAttemptResult> {
    try {
      logger.info(`Tentativa de login: ${loginData.email || loginData.cpf} - Secretaria: ${loginData.secretaria}`)

      // Buscar usuário por email ou CPF
      const user = await this.findUserForLogin(loginData)
      if (!user) {
        return {
          success: false,
          error: 'Usuário não encontrado',
          reason: 'USER_NOT_FOUND'
        }
      }

      // Validar se usuário pode fazer login
      if (!canUserLogin(user)) {
        return {
          success: false,
          error: 'Usuário inativo ou dados incompletos',
          reason: 'USER_INACTIVE'
        }
      }

      // Validar senha
      const passwordValid = await this.userRepository.validatePassword(user, loginData.password)
      if (!passwordValid) {
        logger.warn(`Login falhado - senha inválida: ${user.email || user.cpf}`)
        return {
          success: false,
          error: 'Senha inválida',
          reason: 'INVALID_PASSWORD'
        }
      }

      // Obter role e secretaria (sistema híbrido)
      const { secretaria, role } = await this.getUserSecretariaAndRole(user, loginData.secretaria)

      // Criar AuthUser
      const authUser = userToAuthUser(user, secretaria, role)

      // Gerar token JWT
      const token = this.generateJWT(user, secretaria, role)
      const expiresIn = this.parseJWTExpiration()

      // Registrar login
      await this.registerLogin(user.id, authUser)

      logger.info(`✅ Login bem-sucedido: ${authUser.nome} (${secretaria})`)

      return {
        success: true,
        user: authUser,
        token,
        expiresIn
      }

    } catch (error) {
      logger.error('Erro durante login:', error)
      return {
        success: false,
        error: 'Erro interno do servidor',
        reason: 'INTERNAL_ERROR'
      }
    }
  }

  /**
   * 🔍 Busca usuário para login (email ou CPF)
   */
  private async findUserForLogin(loginData: LoginDto): Promise<User | null> {
    if (loginData.email) {
      return this.userRepository.findByEmail(loginData.email)
    }
    
    if (loginData.cpf) {
      return this.userRepository.findByCPF(loginData.cpf)
    }
    
    return null
  }

  /**
   * 🏛️ Obter secretaria e role do usuário (sistema híbrido)
   */
  private async getUserSecretariaAndRole(
    user: User, 
    requestedSecretaria: SecretariaKey
  ): Promise<{ secretaria: SecretariaKey; role: UserRole }> {
    
    if (this.isUsingRealUsers) {
      // Dados reais - implementar lógica baseada na estrutura real
      // Por enquanto, usar departamentos e lógica padrão
      const departments = await this.userRepository.getUserDepartments(user.id)
      
      // Lógica para mapear departamentos para secretarias
      // Implementar baseado na estrutura real quando disponível
      return {
        secretaria: requestedSecretaria, // Usar solicitada por enquanto
        role: user.servidor ? 'operador' : 'consulta' // Lógica básica
      }
    } else {
      // Dados mock - usar mapeamento predefinido
      const mapping = MOCK_USER_MAPPINGS[user.id]
      if (mapping) {
        return mapping
      }
      
      // Fallback
      return {
        secretaria: requestedSecretaria,
        role: 'consulta'
      }
    }
  }

  /**
   * 🎫 Gerar token JWT
   */
  private generateJWT(user: User, secretaria: SecretariaKey, role: UserRole): string {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email || undefined,
      cpf: user.cpf,
      nome: user.nome,
      secretaria,
      role,
      servidor: user.servidor
    }

    return jwt.sign(payload, this.config.jwtSecret, {
      expiresIn: this.config.jwtExpiresIn
    })
  }

  /**
   * ✅ Verificar e decodificar token JWT
   */
  async verifyToken(token: string): Promise<JwtPayload | null> {
    try {
      const decoded = jwt.verify(token, this.config.jwtSecret) as JwtPayload
      return decoded
    } catch (error) {
      logger.warn('Token inválido:', error)
      return null
    }
  }

  /**
   * 🔍 Obter usuário autenticado pelo token
   */
  async getAuthenticatedUser(token: string): Promise<AuthUser | null> {
    try {
      const payload = await this.verifyToken(token)
      if (!payload) return null

      const user = await this.userRepository.findById(payload.sub)
      if (!user || !canUserLogin(user)) return null

      return userToAuthUser(user, payload.secretaria, payload.role)
    } catch (error) {
      logger.error('Erro ao obter usuário autenticado:', error)
      return null
    }
  }

  /**
   * 🚪 Logout
   */
  async logout(userId: string): Promise<boolean> {
    try {
      // Em sistemas reais, invalidar token em blacklist
      // Por enquanto, apenas log
      logger.info(`Logout realizado para usuário: ${userId}`)
      return true
    } catch (error) {
      logger.error('Erro durante logout:', error)
      return false
    }
  }

  /**
   * 🔄 Refresh token
   */
  async refreshToken(oldToken: string): Promise<string | null> {
    try {
      const payload = await this.verifyToken(oldToken)
      if (!payload) return null

      const user = await this.userRepository.findById(payload.sub)
      if (!user || !canUserLogin(user)) return null

      return this.generateJWT(user, payload.secretaria, payload.role)
    } catch (error) {
      logger.error('Erro ao renovar token:', error)
      return null
    }
  }

  /**
   * 🔐 Alterar senha
   */
  async changePassword(userId: string, oldPassword: string, newPassword: string): Promise<boolean> {
    try {
      const user = await this.userRepository.findById(userId)
      if (!user) return false

      const validPassword = await this.userRepository.validatePassword(user, oldPassword)
      if (!validPassword) return false

      await this.userRepository.update(userId, { nova_senha: newPassword })
      
      logger.info(`Senha alterada para usuário: ${userId}`)
      return true
    } catch (error) {
      logger.error('Erro ao alterar senha:', error)
      return false
    }
  }

  /**
   * 📝 Registrar login no sistema
   */
  private async registerLogin(userId: string, authUser: AuthUser): Promise<void> {
    try {
      await this.userRepository.updateLastLogin(userId)
      
      // Log de auditoria
      logger.info(`Login registrado: ${authUser.nome} (${authUser.secretaria}) - Usando: ${this.isUsingRealUsers ? 'REAL' : 'MOCK'}`)
    } catch (error) {
      logger.error('Erro ao registrar login:', error)
    }
  }

  /**
   * ⏰ Parsear expiração do JWT em segundos
   */
  private parseJWTExpiration(): number {
    const exp = this.config.jwtExpiresIn
    
    if (exp.endsWith('h')) {
      return parseInt(exp) * 3600
    } else if (exp.endsWith('d')) {
      return parseInt(exp) * 24 * 3600
    } else if (exp.endsWith('m')) {
      return parseInt(exp) * 60
    }
    
    return 86400 // Default: 24h
  }

  /**
   * 📊 Obter informações do sistema
   */
  getSystemInfo(): { 
    usingRealUsers: boolean; 
    userCount: Promise<number>; 
    version: string 
  } {
    return {
      usingRealUsers: this.isUsingRealUsers,
      userCount: this.userRepository.count({}),
      version: '1.0.0'
    }
  }

  /**
   * 🔄 Forçar migração para dados reais
   */
  async migrateToRealUsers(): Promise<boolean> {
    logger.info('🔄 Iniciando migração para dados reais...')
    
    const success = await this.tryInitializeRealRepository()
    if (success) {
      logger.info('✅ Migração para dados reais concluída')
    } else {
      logger.warn('❌ Migração para dados reais falhou - mantendo mock')
    }
    
    return success
  }

  /**
   * 🎭 Forçar uso de dados mock
   */
  forceUseMockUsers(): void {
    this.initializeMockRepository()
    logger.info('🎭 Forçado uso de dados mock')
  }
}

// Singleton instance
let authServiceInstance: AuthService | null = null

/**
 * 🏭 Factory para obter instância do AuthService
 */
export function getAuthService(): AuthService {
  if (!authServiceInstance) {
    authServiceInstance = new AuthService()
  }
  return authServiceInstance
}

/**
 * 🔄 Reset da instância (para testes)
 */
export function resetAuthService(): void {
  authServiceInstance = null
}