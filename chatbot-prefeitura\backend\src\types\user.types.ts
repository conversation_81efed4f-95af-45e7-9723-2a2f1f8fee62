/**
 * 👥 Types de Usuário - Baseados na Estrutura Real
 * Interfaces baseadas na tabela 'usuarios' descoberta na investigação
 */

/**
 * 🔑 Interface principal do usuário (baseada na tabela real)
 */
export interface User {
  /** ID único do usuário */
  id: string
  
  /** Nome completo do usuário */
  nome: string
  
  /** Email do usuário (pode ser null na tabela real) */
  email?: string
  
  /** CPF do usuário */
  cpf: string
  
  /** Hash da senha */
  senha?: string
  
  /** Nova senha (para redefinição) */
  nova_senha?: string
  
  /** Se deve redefinir senha no próximo login */
  redefinir_senha: boolean
  
  /** Se a conta está ativa */
  conta_ativa: boolean
  
  /** Data de validade da nova senha */
  data_validade_nova_senha?: Date
  
  /** Data de validade da senha atual */
  data_validade_senha?: Date
  
  /** URL da foto de perfil */
  foto_perfil?: string
  
  /** Se é servidor público */
  servidor: boolean
  
  /** Data de criação */
  created_at?: Date
  
  /** Data de atualização */
  updated_at?: Date
  
  /** ID do Facebook (login social) */
  facebook_id?: string
  
  /** ID do Google (login social) */
  google_id?: string
  
  /** Segunda senha (segurança adicional) */
  segunda_senha?: string
  
  /** Se foi cadastro manual */
  cadastro_manual: boolean
  
  /** Se foi excluído (soft delete) */
  excluido: boolean
  
  /** Registro médico */
  registro_medico?: string
  
  /** UF do registro médico */
  registro_medico_uf?: string
  
  /** Se pode visualizar processos sigilosos */
  visualizar_processo_sigiloso: boolean
  
  /** Código CNS (Cartão Nacional de Saúde) */
  codigo_cns?: string
}

/**
 * 🏛️ Interface para departamentos
 */
export interface Departamento {
  id: string
  created_at?: Date
  updated_at?: Date
  descricao?: string
  endereco?: string
  horario_atendimento?: string
  ativo?: boolean
  icone: string
  id_gestao?: string
  id_usuario_responsavel?: string
  dominio?: string
  tipo?: string
  email?: string
  id_departamento_pai?: string
  slogan?: string
  quantidade_alunos?: number
  id_outros_sistema?: string
  permitir_publicao_portal: boolean
}

/**
 * 🔗 Interface para relacionamento usuário-departamento
 */
export interface UsuarioDepartamento {
  id: string
  id_usuario: string
  id_departamento: string
  created_at?: Date
  updated_at?: Date
}

/**
 * 🎯 Interface simplificada para autenticação
 */
export interface AuthUser {
  id: string
  nome: string
  email: string
  secretaria: string
  role: UserRole
  conta_ativa: boolean
  servidor?: boolean
  foto_perfil?: string
}

/**
 * 👤 Roles de usuário no sistema
 */
export type UserRole = 'admin' | 'gestor' | 'operador' | 'consulta'

/**
 * 🏛️ Secretarias disponíveis
 */
export type SecretariaKey = 
  | 'administracao'
  | 'financas' 
  | 'saude'
  | 'educacao'
  | 'obras'
  | 'social'
  | 'meio_ambiente'

/**
 * 📝 Interface para criação de usuário
 */
export interface CreateUserDto {
  nome: string
  email?: string
  cpf: string
  senha: string
  secretaria: SecretariaKey
  role?: UserRole
  servidor?: boolean
  foto_perfil?: string
}

/**
 * 🔄 Interface para atualização de usuário
 */
export interface UpdateUserDto {
  nome?: string
  email?: string
  foto_perfil?: string
  conta_ativa?: boolean
  nova_senha?: string
  redefinir_senha?: boolean
}

/**
 * 🔐 Interface para login
 */
export interface LoginDto {
  email?: string
  cpf?: string
  password: string
  secretaria: SecretariaKey
}

/**
 * 🎫 Interface para dados do JWT
 */
export interface JwtPayload {
  sub: string // user id
  email?: string
  cpf: string
  nome: string
  secretaria: SecretariaKey
  role: UserRole
  servidor?: boolean
  iat?: number
  exp?: number
}

/**
 * 📊 Interface para resposta de autenticação
 */
export interface AuthResponse {
  user: AuthUser
  token: string
  expiresIn: number
  tokenType: 'Bearer'
}

/**
 * 🔍 Interface para busca de usuários
 */
export interface UserSearchParams {
  nome?: string
  email?: string
  cpf?: string
  secretaria?: SecretariaKey
  role?: UserRole
  conta_ativa?: boolean
  servidor?: boolean
  limit?: number
  offset?: number
}

/**
 * 📈 Interface para estatísticas de usuários
 */
export interface UserStats {
  total: number
  ativos: number
  servidores: number
  por_secretaria: Record<SecretariaKey, number>
  por_role: Record<UserRole, number>
}

/**
 * 🔄 Função para converter User em AuthUser
 */
export function userToAuthUser(user: User, secretaria: SecretariaKey, role: UserRole): AuthUser {
  return {
    id: user.id,
    nome: user.nome,
    email: user.email || '',
    secretaria,
    role,
    conta_ativa: user.conta_ativa,
    servidor: user.servidor,
    foto_perfil: user.foto_perfil
  }
}

/**
 * ✅ Função para validar se usuário pode fazer login
 */
export function canUserLogin(user: User): boolean {
  return (
    user.conta_ativa && 
    !user.excluido &&
    (user.email || user.cpf) && // Precisa ter email OU cpf
    user.senha // Precisa ter senha
  )
}

/**
 * 🏛️ Mapeamento de secretarias
 */
export const SECRETARIAS: Record<SecretariaKey, { nome: string; codigo: string }> = {
  administracao: { nome: 'Secretaria de Administração', codigo: 'ADMIN' },
  financas: { nome: 'Secretaria de Finanças', codigo: 'FIN' },
  saude: { nome: 'Secretaria de Saúde', codigo: 'SAUDE' },
  educacao: { nome: 'Secretaria de Educação', codigo: 'EDU' },
  obras: { nome: 'Secretaria de Obras e Urbanismo', codigo: 'OBRAS' },
  social: { nome: 'Secretaria de Assistência Social', codigo: 'SOCIAL' },
  meio_ambiente: { nome: 'Secretaria de Meio Ambiente', codigo: 'MEIOAMB' }
}

/**
 * 👤 Mapeamento de roles
 */
export const USER_ROLES: Record<UserRole, { nome: string; nivel: number }> = {
  admin: { nome: 'Administrador', nivel: 4 },
  gestor: { nome: 'Gestor', nivel: 3 },
  operador: { nome: 'Operador', nivel: 2 },
  consulta: { nome: 'Consulta', nivel: 1 }
}