'use client'

import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

/**
 * 🎨 Variantes de botões com branding PV
 * Baseadas no design system da logo PVAlinhada
 */
const brandButtonVariants = cva(
  // Classes base
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        // Gradiente principal da logo (azul royal → ciano)
        primary: 'bg-gradient-pv text-white hover:shadow-pv-lg hover:scale-[1.02] active:scale-[0.98]',
        
        // Gradiente reverso
        secondary: 'bg-gradient-pv-reverse text-white hover:shadow-pv-lg hover:scale-[1.02] active:scale-[0.98]',
        
        // Dourado da logo  
        gold: 'bg-gradient-gold text-pv-gray-dark hover:shadow-glow-gold hover:scale-[1.02] active:scale-[0.98]',
        
        // Outline com cores da marca
        outline: 'border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-primary-foreground hover:shadow-pv',
        
        // Outline dourado
        'outline-gold': 'border-2 border-pv-gold text-pv-gold bg-transparent hover:bg-gradient-gold hover:text-pv-gray-dark hover:shadow-glow-gold',
        
        // Ghost com hover gradiente
        ghost: 'bg-transparent text-pv-gray hover:bg-gradient-pv hover:text-white hover:shadow-pv',
        
        // Link estilizado
        link: 'text-primary underline-offset-4 hover:underline hover:text-secondary transition-colors',
        
        // Sutil com gradiente de fundo
        subtle: 'bg-gradient-subtle text-pv-gray border border-border hover:shadow-pv hover:border-primary/30',
        
        // Destaque/Call-to-Action
        cta: 'bg-gradient-pv text-white font-semibold shadow-pv hover:shadow-pv-lg hover:scale-[1.05] active:scale-[0.95] glow-pv',
        
        // Perigo/Destrutivo
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:shadow-lg',
      },
      size: {
        sm: 'h-9 px-3 text-xs',
        default: 'h-10 px-4 py-2',
        lg: 'h-11 px-6 text-base',
        xl: 'h-12 px-8 text-lg',
        icon: 'h-10 w-10',
      },
      rounded: {
        default: 'rounded-md',
        full: 'rounded-full',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
      }
    },
    defaultVariants: {
      variant: 'primary',
      size: 'default',
      rounded: 'default',
    },
  }
)

export interface BrandButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof brandButtonVariants> {
  asChild?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

/**
 * 🚀 BrandButton - Botão com design system PV
 * 
 * Botão estilizado com as cores e gradientes da logo PVAlinhada.
 * Inclui estados interativos, loading, ícones e acessibilidade.
 */
const BrandButton = React.forwardRef<HTMLButtonElement, BrandButtonProps>(
  ({ className, variant, size, rounded, asChild = false, loading = false, leftIcon, rightIcon, children, disabled, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    
    return (
      <Comp
        className={cn(brandButtonVariants({ variant, size, rounded, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <svg
            className="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 0 1 8-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 0 1 4 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        
        {!loading && leftIcon && (
          <span className="mr-2 flex-shrink-0">
            {leftIcon}
          </span>
        )}
        
        {children}
        
        {rightIcon && (
          <span className="ml-2 flex-shrink-0">
            {rightIcon}
          </span>
        )}
      </Comp>
    )
  }
)

BrandButton.displayName = 'BrandButton'

/**
 * 🎯 Botão com ícone apenas
 */
interface IconButtonProps extends Omit<BrandButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode
  'aria-label': string
}

export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, ...props }, ref) => {
    return (
      <BrandButton
        ref={ref}
        size="icon"
        {...props}
      >
        {icon}
      </BrandButton>
    )
  }
)

IconButton.displayName = 'IconButton'

/**
 * ⭐ Botão flutuante de ação (FAB)
 */
export const FloatingActionButton = React.forwardRef<HTMLButtonElement, BrandButtonProps>(
  ({ className, ...props }, ref) => {
    return (
      <BrandButton
        ref={ref}
        variant="cta"
        size="icon"
        rounded="full"
        className={cn(
          'fixed bottom-6 right-6 h-14 w-14 shadow-2xl hover:shadow-glow-pv z-50',
          'hover:scale-110 active:scale-95',
          className
        )}
        {...props}
      />
    )
  }
)

FloatingActionButton.displayName = 'FloatingActionButton'

/**
 * 📊 Grupo de botões
 */
interface ButtonGroupProps {
  children: React.ReactNode
  className?: string
  orientation?: 'horizontal' | 'vertical'
}

export function ButtonGroup({ 
  children, 
  className, 
  orientation = 'horizontal' 
}: ButtonGroupProps) {
  return (
    <div 
      className={cn(
        'inline-flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        '[&>button]:rounded-none',
        '[&>button:first-child]:rounded-l-md',
        '[&>button:last-child]:rounded-r-md',
        orientation === 'vertical' && '[&>button:first-child]:rounded-t-md [&>button:first-child]:rounded-l-none',
        orientation === 'vertical' && '[&>button:last-child]:rounded-b-md [&>button:last-child]:rounded-r-none',
        '[&>button:not(:last-child)]:border-r-0',
        orientation === 'vertical' && '[&>button:not(:last-child)]:border-r [&>button:not(:last-child)]:border-b-0',
        className
      )}
      role="group"
    >
      {children}
    </div>
  )
}

/**
 * 🎨 Botão com gradiente animado
 */
export const AnimatedGradientButton = React.forwardRef<HTMLButtonElement, BrandButtonProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <BrandButton
        ref={ref}
        className={cn(
          'relative overflow-hidden',
          'before:absolute before:inset-0 before:bg-gradient-to-r before:from-pv-royal before:via-pv-primary before:to-pv-cyan',
          'before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700',
          className
        )}
        {...props}
      >
        <span className="relative z-10">
          {children}
        </span>
      </BrandButton>
    )
  }
)

AnimatedGradientButton.displayName = 'AnimatedGradientButton'

export { BrandButton, brandButtonVariants }