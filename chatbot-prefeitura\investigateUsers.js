/**
 * 🔍 Script de Investigação da Tabela de Usuários
 * Versão JavaScript simples para análise da estrutura e dados
 */

const { Client } = require('pg');
require('dotenv').config();

// Configuração do PostgreSQL
const client = new Client({
  host: '*************',
  port: 5411,
  database: 'prefeituravirtual',
  user: 'otto',
  password: 'otto'
});

class UserTableInvestigator {
  constructor() {
    this.client = client;
  }

  /**
   * 🔎 Investigação completa das tabelas de usuários
   */
  async investigate() {
    console.log('🔍 === INVESTIGAÇÃO DE USUÁRIOS - PREFEITURA VIRTUAL ===\n');
    
    try {
      // Conectar ao banco
      await this.client.connect();
      console.log('✅ Conectado ao PostgreSQL\n');
      
      // Investigar tabelas relacionadas a usuários
      const userTables = [
        'usuarios',
        'users', 
        'funcionarios',
        'servidores',
        'pessoas',
        'departamentos',
        'secretarias'
      ];
      
      for (const tableName of userTables) {
        await this.investigateTable(tableName);
        console.log('\n' + '='.repeat(80) + '\n');
      }
      
      // Buscar outras tabelas relacionadas
      await this.findRelatedTables();
      
    } catch (error) {
      console.error('❌ Erro na investigação:', error.message);
    } finally {
      await this.client.end();
    }
  }

  /**
   * 🔍 Investiga uma tabela específica
   */
  async investigateTable(tableName) {
    console.log(`🔎 INVESTIGANDO TABELA: ${tableName.toUpperCase()}`);
    console.log('-'.repeat(50));
    
    try {
      // Verificar se tabela existe
      const exists = await this.checkTableExists(tableName);
      if (!exists) {
        console.log(`❌ Tabela '${tableName}' não encontrada`);
        return null;
      }
      
      // Obter estrutura da tabela
      const columns = await this.getTableStructure(tableName);
      console.log(`✅ Tabela '${tableName}' encontrada - ${columns.length} colunas`);
      
      // Obter contagem de registros
      const rowCount = await this.getRowCount(tableName);
      console.log(`📊 Registros: ${rowCount}`);
      
      // Mostrar estrutura
      this.displayTableStructure(columns);
      
      if (rowCount > 0) {
        // Obter dados de exemplo
        const sampleData = await this.getSampleData(tableName, 3);
        this.displaySampleData(sampleData, tableName);
        
        // Se for tabela de usuários, fazer análises específicas
        if (tableName === 'usuarios' && rowCount > 0) {
          await this.analyzeUserTable(tableName, columns);
        }
      } else {
        console.log('ℹ️  Tabela vazia - sem dados para análise');
      }
      
    } catch (error) {
      console.log(`❌ Erro ao investigar tabela '${tableName}':`, error.message);
      return null;
    }
  }

  /**
   * ✅ Verifica se tabela existe
   */
  async checkTableExists(tableName) {
    try {
      const query = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        );
      `;
      const result = await this.client.query(query, [tableName]);
      return result.rows[0].exists;
    } catch (error) {
      return false;
    }
  }

  /**
   * 📋 Obtém estrutura da tabela
   */
  async getTableStructure(tableName) {
    const query = `
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns
      WHERE table_schema = 'public' 
      AND table_name = $1
      ORDER BY ordinal_position;
    `;
    const result = await this.client.query(query, [tableName]);
    return result.rows;
  }

  /**
   * 🔢 Obtém contagem de registros
   */
  async getRowCount(tableName) {
    const query = `SELECT COUNT(*) as count FROM "${tableName}"`;
    const result = await this.client.query(query);
    return parseInt(result.rows[0].count);
  }

  /**
   * 📝 Obtém dados de exemplo
   */
  async getSampleData(tableName, limit = 5) {
    const query = `SELECT * FROM "${tableName}" LIMIT $1`;
    const result = await this.client.query(query, [limit]);
    return result.rows;
  }

  /**
   * 👥 Análise específica da tabela usuarios
   */
  async analyzeUserTable(tableName, columns) {
    console.log('\n🔐 ANÁLISE ESPECÍFICA DE USUÁRIOS:');
    console.log('-'.repeat(30));
    
    // Verificar campos relacionados à autenticação
    const authFields = ['password', 'senha', 'pass', 'pwd', 'hash'];
    const emailFields = ['email', 'mail', 'usuario', 'login'];
    const nameFields = ['nome', 'name', 'full_name', 'nome_completo'];
    const deptFields = ['departamento', 'secretaria', 'setor', 'department'];
    
    console.log('🔑 Campos de autenticação encontrados:');
    authFields.forEach(field => {
      const found = columns.find(col => col.column_name.toLowerCase().includes(field));
      if (found) {
        console.log(`  ✅ ${found.column_name} (${found.data_type})`);
      }
    });
    
    console.log('\n📧 Campos de identificação encontrados:');
    emailFields.forEach(field => {
      const found = columns.find(col => col.column_name.toLowerCase().includes(field));
      if (found) {
        console.log(`  ✅ ${found.column_name} (${found.data_type})`);
      }
    });
    
    console.log('\n👤 Campos de nome encontrados:');
    nameFields.forEach(field => {
      const found = columns.find(col => col.column_name.toLowerCase().includes(field));
      if (found) {
        console.log(`  ✅ ${found.column_name} (${found.data_type})`);
      }
    });
    
    console.log('\n🏛️ Campos de departamento encontrados:');
    deptFields.forEach(field => {
      const found = columns.find(col => col.column_name.toLowerCase().includes(field));
      if (found) {
        console.log(`  ✅ ${found.column_name} (${found.data_type})`);
      }
    });
    
    // Analisar possível criptografia das senhas
    await this.analyzePossiblePasswordEncryption(tableName, columns);
  }

  /**
   * 🔐 Analisa possível criptografia de senhas
   */
  async analyzePossiblePasswordEncryption(tableName, columns) {
    const passwordField = columns.find(col => 
      ['password', 'senha', 'pass', 'pwd', 'hash'].some(field => 
        col.column_name.toLowerCase().includes(field)
      )
    );
    
    if (passwordField) {
      console.log(`\n🔐 ANÁLISE DE CRIPTOGRAFIA (${passwordField.column_name}):`);
      
      try {
        const query = `
          SELECT 
            "${passwordField.column_name}" as password,
            LENGTH("${passwordField.column_name}") as password_length
          FROM "${tableName}" 
          WHERE "${passwordField.column_name}" IS NOT NULL
          LIMIT 3
        `;
        const result = await this.client.query(query);
        
        result.rows.forEach((row, index) => {
          const password = row.password;
          const length = row.password_length;
          
          console.log(`  Exemplo ${index + 1}: Length=${length}`);
          
          // Detectar tipo de hash baseado no comprimento e padrão
          if (length === 32 && /^[a-f0-9]+$/i.test(password)) {
            console.log(`    🔍 Provavelmente MD5`);
          } else if (length === 40 && /^[a-f0-9]+$/i.test(password)) {
            console.log(`    🔍 Provavelmente SHA1`);
          } else if (length === 60 && password.startsWith('$2')) {
            console.log(`    🔍 Provavelmente bcrypt`);
          } else if (length === 64 && /^[a-f0-9]+$/i.test(password)) {
            console.log(`    🔍 Provavelmente SHA256`);
          } else {
            console.log(`    🔍 Tipo de hash desconhecido ou texto simples`);
          }
        });
      } catch (error) {
        console.log('    ❌ Erro ao analisar senhas:', error.message);
      }
    }
  }

  /**
   * 📊 Exibe estrutura da tabela
   */
  displayTableStructure(columns) {
    console.log('\n📋 ESTRUTURA DA TABELA:');
    columns.forEach(col => {
      const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
      const maxLength = col.character_maximum_length ? ` (${col.character_maximum_length})` : '';
      console.log(`  • ${col.column_name}: ${col.data_type}${maxLength} ${nullable}`);
    });
  }

  /**
   * 📄 Exibe dados de exemplo
   */
  displaySampleData(data, tableName) {
    if (data.length === 0) return;
    
    console.log(`\n📄 DADOS DE EXEMPLO (${tableName}):`);
    console.log('Primeiros', data.length, 'registros:');
    
    data.forEach((row, index) => {
      console.log(`\n  Registro ${index + 1}:`);
      Object.entries(row).forEach(([key, value]) => {
        // Mascarar campos sensíveis
        if (['password', 'senha', 'pass', 'pwd', 'hash'].some(field => key.toLowerCase().includes(field))) {
          console.log(`    ${key}: [SENHA OCULTA - Length: ${String(value).length}]`);
        } else {
          console.log(`    ${key}: ${value}`);
        }
      });
    });
  }

  /**
   * 🔍 Busca tabelas relacionadas
   */
  async findRelatedTables() {
    console.log('🔍 BUSCANDO OUTRAS TABELAS RELACIONADAS:');
    console.log('-'.repeat(50));
    
    try {
      const query = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND (
          table_name ILIKE '%user%' OR
          table_name ILIKE '%pessoa%' OR
          table_name ILIKE '%funcionario%' OR
          table_name ILIKE '%servidor%' OR
          table_name ILIKE '%auth%' OR
          table_name ILIKE '%login%' OR
          table_name ILIKE '%departamento%' OR
          table_name ILIKE '%secretaria%'
        )
        ORDER BY table_name;
      `;
      
      const result = await this.client.query(query);
      
      console.log(`📋 Encontradas ${result.rows.length} tabelas relacionadas:`);
      result.rows.forEach(row => {
        console.log(`  • ${row.table_name}`);
      });
    } catch (error) {
      console.log('❌ Erro ao buscar tabelas relacionadas:', error.message);
    }
  }

  /**
   * 📊 Gera relatório final
   */
  async generateReport() {
    console.log('\n📊 === RELATÓRIO FINAL ===');
    console.log('Este script investigou as tabelas de usuários do sistema.');
    console.log('Use as informações acima para definir a estratégia de integração.');
    console.log('\nPróximos passos sugeridos:');
    console.log('1. Analisar a estrutura encontrada');
    console.log('2. Definir mapeamento de campos');
    console.log('3. Implementar service de usuários reais');
    console.log('4. Adaptar sistema de autenticação');
  }
}

// Executar investigação
const investigator = new UserTableInvestigator();

investigator.investigate()
  .then(() => investigator.generateReport())
  .catch(error => {
    console.error('❌ Erro na investigação:', error);
    process.exit(1);
  });