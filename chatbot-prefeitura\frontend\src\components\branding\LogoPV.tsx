'use client'

import Image from 'next/image'
import { cn } from '@/lib/utils'

interface LogoPVProps {
  /** <PERSON>ariante da logo */
  variant?: 'principal' | 'horizontal' | 'icone'
  /** Tamanho da logo */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'custom'
  /** Largura customizada (apenas para size="custom") */
  width?: number
  /** Altura customizada (apenas para size="custom") */
  height?: number
  /** Classes CSS adicionais */
  className?: string
  /** Se deve ter prioridade de carregamento */
  priority?: boolean
  /** Callback para clique */
  onClick?: () => void
}

// Mapeamento de tamanhos predefinidos
const sizeMap = {
  xs: { width: 60, height: 30 },
  sm: { width: 100, height: 50 },
  md: { width: 150, height: 75 },
  lg: { width: 200, height: 100 },
  xl: { width: 300, height: 150 },
}

/**
 * 🏢 Componente Logo PV (Prefeitura Virtual)
 * 
 * Exibe a logo PVAlinhada.png com diferentes variantes e tamanhos.
 * Inclui fallbacks, otimizações e estados interativos.
 */
export function LogoPV({
  variant = 'principal',
  size = 'md',
  width,
  height,
  className,
  priority = false,
  onClick,
  ...props
}: LogoPVProps) {
  // Determinar dimensões baseadas no tamanho
  const dimensions = size === 'custom' && width && height 
    ? { width, height }
    : sizeMap[size as keyof typeof sizeMap]

  // Determinar caminho da logo baseado na variante
  const logoSrc = `/assets/logos/empresa/PVAlinhada.png`

  return (
    <div 
      className={cn(
        'relative flex items-center justify-center',
        onClick && 'cursor-pointer transition-transform hover:scale-105',
        className
      )}
      onClick={onClick}
      {...props}
    >
      <Image
        src={logoSrc}
        alt="Prefeitura Virtual - Logo"
        width={dimensions.width}
        height={dimensions.height}
        priority={priority}
        className={cn(
          'object-contain transition-all duration-300',
          'drop-shadow-sm',
          onClick && 'hover:drop-shadow-md'
        )}
        quality={90}
      />
    </div>
  )
}

/**
 * 🎯 Componente Logo Animada
 * Logo com efeitos visuais especiais
 */
interface AnimatedLogoPVProps extends LogoPVProps {
  /** Tipo de animação */
  animation?: 'glow' | 'pulse' | 'bounce' | 'none'
}

export function AnimatedLogoPV({
  animation = 'glow',
  className,
  ...props
}: AnimatedLogoPVProps) {
  const animationClasses = {
    glow: 'glow-pv animate-pulse-subtle',
    pulse: 'animate-pulse-subtle',
    bounce: 'animate-bounce-subtle',
    none: '',
  }

  return (
    <LogoPV
      className={cn(
        animationClasses[animation],
        className
      )}
      {...props}
    />
  )
}

/**
 * 💫 Componente Logo com Texto
 * Logo acompanhada de texto institucional
 */
interface LogoWithTextProps extends Omit<LogoPVProps, 'variant'> {
  /** Texto a ser exibido */
  text?: string
  /** Posição do texto */
  textPosition?: 'right' | 'bottom'
  /** Tamanho do texto */
  textSize?: 'sm' | 'md' | 'lg'
}

export function LogoPVWithText({
  text = 'Prefeitura Virtual',
  textPosition = 'right',
  textSize = 'md',
  className,
  ...props
}: LogoWithTextProps) {
  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  const containerClasses = textPosition === 'right' 
    ? 'flex items-center gap-4'
    : 'flex flex-col items-center gap-2'

  return (
    <div className={cn(containerClasses, className)}>
      <LogoPV {...props} />
      <span className={cn(
        'font-semibold text-pv-gray',
        textSizeClasses[textSize]
      )}>
        {text}
      </span>
    </div>
  )
}

/**
 * 🎨 Componente Logo com Gradiente
 * Logo com efeito de gradiente da marca
 */
export function LogoPVGradient({
  className,
  ...props
}: LogoPVProps) {
  return (
    <div className={cn(
      'relative p-4 rounded-lg bg-gradient-pv',
      'shadow-pv-lg',
      className
    )}>
      <LogoPV 
        {...props}
        className="drop-shadow-lg"
      />
      {/* Overlay sutil para destacar a logo */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-lg pointer-events-none" />
    </div>
  )
}